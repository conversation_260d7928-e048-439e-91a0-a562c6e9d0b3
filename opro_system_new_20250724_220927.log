2025-07-24 22:09:27,655 - __main__ - INFO - ====================================================================================================
2025-07-24 22:09:27,655 - __main__ - INFO - 🚀 OPRO系统启动（重构版本）
2025-07-24 22:09:27,655 - __main__ - INFO - ====================================================================================================
2025-07-24 22:09:27,655 - __main__ - INFO - 运行模式: weekly
2025-07-24 22:09:27,655 - __main__ - INFO - LLM提供商: zhipuai
2025-07-24 22:09:27,655 - __main__ - INFO - 系统架构: 重构版本（Service-Based Architecture）
2025-07-24 22:09:27,655 - __main__ - INFO - OPRO启用: True
2025-07-24 22:09:27,655 - __main__ - INFO - 📊 投资组合跟踪器: 启用（记录每日投资组合状态）
2025-07-24 22:09:27,655 - __main__ - INFO - 💾 持久化路径: 自动生成
2025-07-24 22:09:27,655 - __main__ - INFO - 🔄 跨周状态继承: 启用（支持周间状态传递）
2025-07-24 22:09:27,655 - __main__ - INFO - ✅ 完整模式: 跟踪器=True, 继承状态=True
2025-07-24 22:09:27,655 - __main__ - INFO - 周期性优化启用: 每 7 天
2025-07-24 22:09:27,656 - __main__ - INFO - 初始化重构版本系统...
2025-07-24 22:09:27,760 - __main__ - INFO - 🔧 使用ServiceFactory创建重构版本的评估器...
2025-07-24 22:09:27,761 - __main__ - INFO - 初始化RefactoredContributionAssessor组件...
2025-07-24 22:09:27,763 - contribution_assessment.shapley_calculator.ShapleyCalculator - INFO - Shapley值计算器初始化完成
2025-07-24 22:09:27,763 - state_management.daily_state_manager - INFO - DailyStateManager initialized with storage: data/state_manager.db
2025-07-24 22:09:27,783 - state_management.daily_state_manager - INFO - DailyStateManager initialized with storage: data/state_manager.db
2025-07-24 22:09:27,783 - __main__ - INFO - 联盟服务初始化完成
2025-07-24 22:09:27,783 - __main__ - INFO - 模拟服务初始化完成
2025-07-24 22:09:27,783 - __main__ - INFO - 简化OPRO服务初始化完成 (with historical data support)
2025-07-24 22:09:27,783 - contribution_assessment.services.state_manager - INFO - StateManager initialized with storage: data/state_manager.db
2025-07-24 22:09:27,784 - __main__ - INFO - ✅ RefactoredContributionAssessor初始化完成
2025-07-24 22:09:27,784 - __main__ - INFO - ✅ 重构版本的评估器创建成功
2025-07-24 22:09:27,784 - __main__ - INFO - ✅ 重构版本系统初始化完成
2025-07-24 22:09:27,784 - __main__ - INFO - 🔄 使用周期性优化模式
2025-07-24 22:09:27,784 - __main__ - INFO - 🔄 运行模式: 周期性在线优化（新架构WeeklyCycleManager）
2025-07-24 22:09:27,784 - __main__ - INFO - 📋 架构特点: 周 > 阶段 的层级结构
2025-07-24 22:09:27,784 - __main__ - INFO - 📋 周期性优化配置:
2025-07-24 22:09:27,784 - __main__ - INFO -    优化频率: 每 7 交易日
2025-07-24 22:09:27,784 - __main__ - INFO -    最少运行天数: 5 天
2025-07-24 22:09:27,784 - __main__ - INFO -    每周期最多优化: 1 个智能体
2025-07-24 22:09:27,784 - __main__ - INFO -    容错模式: 启用
2025-07-24 22:09:27,784 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-24 22:09:27,784 - __main__ - INFO - 🚀 使用WeeklyCycleManager架构（周 > 阶段）
2025-07-24 22:09:27,785 - __main__ - INFO - 📅 日期范围: 2025-01-01 到 2025-02-15
2025-07-24 22:09:27,785 - __main__ - INFO - 📊 交易日总数: 33 天
2025-07-24 22:09:27,785 - __main__ - INFO - 🔄 自动计算周数: 6 周 (每周5个交易日)
2025-07-24 22:09:27,789 - __main__ - INFO - 📊 启用日常投资组合跟踪器 (持久化: data/portfolio_tracker_20250724_220927.json)
2025-07-24 22:09:27,809 - __main__ - INFO - ✅ 使用WeeklyOptimizationService作为优化执行器
2025-07-24 22:09:27,809 - __main__ - INFO - ✅ WeeklyCycleManager组件初始化成功
2025-07-24 22:09:27,809 - __main__ - INFO - ✅ WeeklyCycleManager创建成功，开始执行周期性优化
2025-07-24 22:09:27,809 - __main__ - INFO - 🚀 开始6周的优化循环
2025-07-24 22:09:27,809 - __main__ - INFO - 
📅 === 第1周优化循环 ===
2025-07-24 22:09:27,809 - __main__ - INFO - 🚀 开始第 1 周优化周期 (6阶段工作流)
2025-07-24 22:09:27,809 - __main__ - INFO - 创建智能体配置...
2025-07-24 22:09:27,810 - __main__ - INFO - 使用新架构创建智能体
2025-07-24 22:09:27,835 - __main__ - INFO - OPRO智能体 NAA 初始化完成 (OPRO: 启用)
2025-07-24 22:09:27,835 - __main__ - INFO - 创建智能体: NAA (OPRO: 启用)
2025-07-24 22:09:27,835 - __main__ - INFO - ✅ 智能体 NAA 创建成功
2025-07-24 22:09:27,835 - __main__ - INFO - OPRO智能体 TAA 初始化完成 (OPRO: 启用)
2025-07-24 22:09:27,835 - __main__ - INFO - 创建智能体: TAA (OPRO: 启用)
2025-07-24 22:09:27,835 - __main__ - INFO - ✅ 智能体 TAA 创建成功
2025-07-24 22:09:27,835 - __main__ - INFO - OPRO智能体 FAA 初始化完成 (OPRO: 启用)
2025-07-24 22:09:27,835 - __main__ - INFO - 创建智能体: FAA (OPRO: 启用)
2025-07-24 22:09:27,835 - __main__ - INFO - ✅ 智能体 FAA 创建成功
2025-07-24 22:09:27,835 - __main__ - INFO - OPRO智能体 BOA 初始化完成 (OPRO: 启用)
2025-07-24 22:09:27,835 - __main__ - INFO - 创建智能体: BOA (OPRO: 启用)
2025-07-24 22:09:27,835 - __main__ - INFO - ✅ 智能体 BOA 创建成功
2025-07-24 22:09:27,835 - __main__ - INFO - OPRO智能体 BeOA 初始化完成 (OPRO: 启用)
2025-07-24 22:09:27,835 - __main__ - INFO - 创建智能体: BeOA (OPRO: 启用)
2025-07-24 22:09:27,835 - __main__ - INFO - ✅ 智能体 BeOA 创建成功
2025-07-24 22:09:27,835 - __main__ - INFO - OPRO智能体 NOA 初始化完成 (OPRO: 启用)
2025-07-24 22:09:27,835 - __main__ - INFO - 创建智能体: NOA (OPRO: 启用)
2025-07-24 22:09:27,835 - __main__ - INFO - ✅ 智能体 NOA 创建成功
2025-07-24 22:09:27,835 - __main__ - INFO - OPRO智能体 TRA 初始化完成 (OPRO: 启用)
2025-07-24 22:09:27,835 - __main__ - INFO - 创建智能体: TRA (OPRO: 启用)
2025-07-24 22:09:27,835 - __main__ - INFO - ✅ 智能体 TRA 创建成功
2025-07-24 22:09:27,835 - __main__ - INFO - 智能体创建完成，成功: 7/7
2025-07-24 22:09:27,835 - __main__ - INFO - ✅ 新架构成功创建智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-24 22:09:27,835 - __main__ - INFO - 可用智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-24 22:09:27,835 - __main__ - INFO - 🔄 第1周使用旧版跨周状态传递
2025-07-24 22:09:27,835 - __main__ - INFO - 🔄 开始第 1 周评估周期 (6阶段工作流)
2025-07-24 22:09:27,835 - __main__ - INFO - 📊 Phase 1: 联盟生成与子集运算
2025-07-24 22:09:27,835 - __main__ - INFO - Starting coalition_generation phase for workflow weekly_workflow_weekly_assessment_1_20250724_220927_week_1
2025-07-24 22:09:27,835 - __main__ - INFO - 开始联盟生成: 目标智能体=7, 分析智能体=3, 交易智能体=TRA
2025-07-24 22:09:27,835 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 开始联盟生成和剪枝
2025-07-24 22:09:27,836 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 总智能体数: 7
2025-07-24 22:09:27,836 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 分析智能体: {'TAA', 'NAA', 'FAA'}
2025-07-24 22:09:27,836 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 交易智能体: TRA
2025-07-24 22:09:27,836 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 生成了 128 个初始联盟
2025-07-24 22:09:27,836 - __main__ - INFO - 限制联盟数量: 56 -> 50
2025-07-24 22:09:27,836 - __main__ - INFO - 联盟生成完成: 有效联盟=50, 剪枝联盟=72, 耗时=0.000s
2025-07-24 22:09:27,836 - __main__ - INFO - Coalition generation completed in 0.00s, generated 50 coalitions
2025-07-24 22:09:27,836 - __main__ - INFO - 💹 Phase 2: 交易模拟
2025-07-24 22:09:27,836 - __main__ - INFO - Starting trading_simulation phase for workflow weekly_workflow_weekly_assessment_1_20250724_220927_week_1
2025-07-24 22:09:27,836 - __main__ - INFO - 开始批量模拟: 50 个联盟，最大并发数: 60
2025-07-24 22:09:27,836 - __main__ - INFO - 🚀 开始分阶段交易模拟...
2025-07-24 22:09:27,836 - __main__ - INFO - 📊 模拟计划:
2025-07-24 22:09:27,836 - __main__ - INFO -   - 完整联盟: {'TAA', 'NOA', 'NAA', 'BeOA', 'BOA', 'TRA', 'FAA'} (详细日志)
2025-07-24 22:09:27,836 - __main__ - INFO -   - 子集联盟: 49 个 (简洁日志)
2025-07-24 22:09:27,836 - __main__ - INFO - ======================================================================
2025-07-24 22:09:27,836 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-24 22:09:27,836 - __main__ - INFO - ======================================================================
2025-07-24 22:09:27,836 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-24 22:09:27,836 - __main__ - INFO - 开始模拟联盟: {'TAA', 'NOA', 'NAA', 'BeOA', 'BOA', 'TRA', 'FAA'}
2025-07-24 22:09:27,836 - __main__ - INFO - 📅 当前周数: 1
2025-07-24 22:09:27,836 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'NOA', 'NAA', 'BeOA', 'BOA', 'TRA', 'FAA'})
2025-07-24 22:09:27,837 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=False
2025-07-24 22:09:27,933 - __main__ - INFO - 📅 设置当前模拟周数: 1 (来自参数)
2025-07-24 22:09:27,934 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-24 22:09:27,934 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-24 22:09:27,934 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 新闻22条
2025-07-24 22:09:52,369 - __main__ - INFO - 🤖 NAA 输出: 5 个字段
2025-07-24 22:09:52,371 - __main__ - INFO - 🤖 执行智能体 TAA...
2025-07-24 22:09:52,371 - __main__ - INFO - 🤖 TAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 价格30个点
2025-07-24 22:10:12,402 - __main__ - INFO - 🤖 TAA 输出: 6 个字段
2025-07-24 22:10:12,402 - __main__ - INFO - 🤖 执行智能体 FAA...
2025-07-24 22:10:12,402 - __main__ - INFO - 🤖 FAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 基本面1个股票
2025-07-24 22:10:24,975 - __main__ - INFO - 🤖 FAA 输出: 13 个字段
2025-07-24 22:10:24,977 - __main__ - INFO - ✅ 分析层完成: 3 个智能体
2025-07-24 22:10:24,977 - __main__ - INFO - 🤖 执行展望层智能体 BOA...
2025-07-24 22:10:24,977 - __main__ - INFO - 🤖 BOA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:10:56,414 - __main__ - INFO - 🤖 BOA 输出: 5 个字段
2025-07-24 22:10:56,416 - __main__ - INFO - ✅ 展望层智能体 BOA 完成: 已输出结果
2025-07-24 22:10:56,416 - __main__ - INFO - 🤖 执行展望层智能体 BeOA...
2025-07-24 22:10:56,416 - __main__ - INFO - 🤖 BeOA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:11:12,350 - __main__ - INFO - 🤖 BeOA 输出: 5 个字段
2025-07-24 22:11:12,351 - __main__ - INFO - ✅ 展望层智能体 BeOA 完成: 已输出结果
2025-07-24 22:11:12,351 - __main__ - INFO - 🤖 执行展望层智能体 NOA...
2025-07-24 22:11:12,351 - __main__ - INFO - 🤖 NOA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:11:32,468 - __main__ - INFO - 🤖 NOA 输出: 5 个字段
2025-07-24 22:11:32,469 - __main__ - INFO - ✅ 展望层智能体 NOA 完成: 已输出结果
2025-07-24 22:11:32,469 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-24 22:11:32,469 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:11:49,203 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-24 22:11:49,204 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-24 22:11:49,205 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-24 22:11:49,205 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-24 22:11:49,205 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-24 22:11:49,205 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-24 22:11:49,205 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-24 22:11:49,205 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体信号分析：\n- BOA（看涨展望）给出了较为强烈的看涨信号，主要基于Tech Moguls对2025年的乐观展望和Apple在中国市场对iPhone 16的降价策略。\n- BeOA（看跌展望）没有给出强烈的看跌信号，主要因为UBS对iPhone销售预测的降低和部分投资者对Apple股票的卖出行为。\n- NOA（中性展望）认为应该中性观望，主要基于Apple在中国市场的竞争和整体市场的波动性。\n\n决策理由：\n- 虽然BOA和BeOA的信号方向相反，但考虑到BOA的信号更为强烈，且Apple的基本面数据表现良好，因此倾向于执行买入操作。\n- 风险评估：NAA、TAA、FAA的结果显示，Apple面临的市场竞争和整体市场波动性是潜在的风险因素。\n\n最终决策：买入\n\n', 'action': 'buy', 'trading_actions': {'AAPL': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-02'}
2025-07-24 22:11:49,205 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': 1.0}
2025-07-24 22:11:49,205 - __main__ - INFO - 📈 第1天交易动作: {'AAPL': 1.0}
2025-07-24 22:11:49,215 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-24 22:11:49,215 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-24 22:11:49,215 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 新闻48条
2025-07-24 22:12:29,630 - __main__ - INFO - 🤖 NAA 输出: 5 个字段
2025-07-24 22:12:29,631 - __main__ - INFO - 🤖 执行智能体 TAA...
2025-07-24 22:12:29,631 - __main__ - INFO - 🤖 TAA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 价格30个点
2025-07-24 22:13:06,215 - __main__ - INFO - 🤖 TAA 输出: 6 个字段
2025-07-24 22:13:06,215 - __main__ - INFO - 🤖 执行智能体 FAA...
2025-07-24 22:13:06,215 - __main__ - INFO - 🤖 FAA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 基本面1个股票
2025-07-24 22:13:15,123 - __main__ - INFO - 🤖 FAA 输出: 13 个字段
2025-07-24 22:13:15,123 - __main__ - INFO - ✅ 分析层完成: 3 个智能体
2025-07-24 22:13:15,123 - __main__ - INFO - 🤖 执行展望层智能体 BOA...
2025-07-24 22:13:15,123 - __main__ - INFO - 🤖 BOA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:13:50,417 - __main__ - INFO - 🤖 BOA 输出: 5 个字段
2025-07-24 22:13:50,418 - __main__ - INFO - ✅ 展望层智能体 BOA 完成: 已输出结果
2025-07-24 22:13:50,418 - __main__ - INFO - 🤖 执行展望层智能体 BeOA...
2025-07-24 22:13:50,418 - __main__ - INFO - 🤖 BeOA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:14:12,821 - __main__ - INFO - 🤖 BeOA 输出: 5 个字段
2025-07-24 22:14:12,822 - __main__ - INFO - ✅ 展望层智能体 BeOA 完成: 已输出结果
2025-07-24 22:14:12,822 - __main__ - INFO - 🤖 执行展望层智能体 NOA...
2025-07-24 22:14:12,822 - __main__ - INFO - 🤖 NOA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:14:39,489 - __main__ - INFO - 🤖 NOA 输出: 5 个字段
2025-07-24 22:14:39,489 - __main__ - INFO - ✅ 展望层智能体 NOA 完成: 已输出结果
2025-07-24 22:14:39,489 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-24 22:14:39,489 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:14:57,628 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-24 22:14:57,629 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-24 22:14:57,629 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-24 22:14:57,629 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-24 22:14:57,629 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-24 22:14:57,629 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-24 22:14:57,629 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-24 22:14:57,629 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体信号分析：\n- BOA（看涨展望）给出了多个看涨信号，包括美国第六巡回上诉法院推翻了联邦通信委员会的净中性规则，这对网络自由、网络安全和消费者有利，以及苹果公司就Siri隐私违规问题达成9500万美元的和解协议。\n- BeOA（看跌展望）没有给出任何强烈看跌信号。\n- NOA（中性展望）认为应该中性观望，因为中国正在扩大消费补贴，包括智能手机和其他电子产品。\n\n决策理由：\n- 由于BOA给出了多个看涨信号，而BeOA没有给出任何看跌信号，因此倾向于执行买入操作。\n- 分析层的NAA、TAA、FAA结果作为风险评估的参考，但不是决策主导因素。\n\n风险评估：\n- 苹果公司在中国的市场份额面临挑战，因为政府补贴和支持国内品牌。\n\n最终决策：买入\n\n', 'action': 'buy', 'trading_actions': {'AAPL': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-03'}
2025-07-24 22:14:57,629 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': 1.0}
2025-07-24 22:14:57,630 - __main__ - INFO - 📈 第2天交易动作: {'AAPL': 1.0}
2025-07-24 22:14:57,636 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-24 22:14:57,637 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-24 22:14:57,637 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 新闻32条
