2025-07-25 13:05:30,660 - __main__ - INFO - ====================================================================================================
2025-07-25 13:05:30,660 - __main__ - INFO - 🚀 OPRO系统启动（重构版本）
2025-07-25 13:05:30,660 - __main__ - INFO - ====================================================================================================
2025-07-25 13:05:30,660 - __main__ - INFO - 运行模式: evaluation
2025-07-25 13:05:30,660 - __main__ - INFO - LLM提供商: zhipuai
2025-07-25 13:05:30,660 - __main__ - INFO - 系统架构: 重构版本（Service-Based Architecture）
2025-07-25 13:05:30,660 - __main__ - INFO - OPRO启用: False
2025-07-25 13:05:30,660 - __main__ - INFO - 📊 投资组合跟踪器: 启用（记录每日投资组合状态）
2025-07-25 13:05:30,660 - __main__ - INFO - 💾 持久化路径: 自动生成
2025-07-25 13:05:30,660 - __main__ - INFO - 🔄 跨周状态继承: 启用（支持周间状态传递）
2025-07-25 13:05:30,660 - __main__ - INFO - ✅ 完整模式: 跟踪器=True, 继承状态=True
2025-07-25 13:05:30,661 - __main__ - INFO - 初始化重构版本系统...
2025-07-25 13:05:30,751 - __main__ - INFO - 🔧 使用ServiceFactory创建重构版本的评估器...
2025-07-25 13:05:30,751 - contribution_assessment.infrastructure.configuration_manager - DEBUG - Updated config with 41 items
2025-07-25 13:05:30,751 - contribution_assessment.infrastructure.service_factory - DEBUG - Configured services with 41 configuration items
2025-07-25 13:05:30,751 - __main__ - INFO - 初始化RefactoredContributionAssessor组件...
2025-07-25 13:05:30,751 - contribution_assessment.infrastructure.configuration_manager - DEBUG - Updated config with 41 items
2025-07-25 13:05:30,751 - contribution_assessment.infrastructure.service_factory - DEBUG - Configured services with 41 configuration items
2025-07-25 13:05:30,753 - contribution_assessment.shapley_calculator.ShapleyCalculator - INFO - Shapley值计算器初始化完成
2025-07-25 13:05:30,753 - state_management.daily_state_manager - DEBUG - Daily states table initialized successfully
2025-07-25 13:05:30,754 - state_management.daily_state_manager - INFO - DailyStateManager initialized with storage: data/state_manager.db
2025-07-25 13:05:30,754 - __main__ - DEBUG - DailyStateManager初始化成功
2025-07-25 13:05:30,772 - state_management.daily_state_manager - DEBUG - Daily states table initialized successfully
2025-07-25 13:05:30,772 - state_management.daily_state_manager - INFO - DailyStateManager initialized with storage: data/state_manager.db
2025-07-25 13:05:30,772 - contribution_assessment.infrastructure.event_bus - DEBUG - Added subscription sub_1 for event config_updated
2025-07-25 13:05:30,772 - contribution_assessment.infrastructure.event_bus - DEBUG - Added subscription sub_2 for event performance_alert
2025-07-25 13:05:30,772 - __main__ - DEBUG - 事件订阅完成
2025-07-25 13:05:30,772 - __main__ - INFO - 联盟服务初始化完成
2025-07-25 13:05:30,772 - __main__ - DEBUG - 已加载服务配置: {'start_date': '2025-01-01', 'end_date': '2025-02-15', 'stocks': ['AAPL'], 'starting_cash': 1000000, 'simulation_days': 3, 'max_concurrent_simulations': 60}
2025-07-25 13:05:30,772 - contribution_assessment.infrastructure.event_bus - DEBUG - Added subscription sub_3 for event config_updated
2025-07-25 13:05:30,772 - contribution_assessment.infrastructure.event_bus - DEBUG - Added subscription sub_4 for event performance_alert
2025-07-25 13:05:30,772 - __main__ - DEBUG - 事件订阅完成
2025-07-25 13:05:30,772 - __main__ - INFO - 模拟服务初始化完成
2025-07-25 13:05:30,772 - __main__ - INFO - 简化OPRO服务初始化完成 (with historical data support)
2025-07-25 13:05:30,772 - contribution_assessment.services.state_manager - DEBUG - Database initialized successfully
2025-07-25 13:05:30,773 - contribution_assessment.services.state_manager - INFO - StateManager initialized with storage: data/state_manager.db
2025-07-25 13:05:30,773 - __main__ - INFO - ✅ RefactoredContributionAssessor初始化完成
2025-07-25 13:05:30,773 - __main__ - INFO - ✅ 重构版本的评估器创建成功
2025-07-25 13:05:30,773 - __main__ - INFO - ✅ 重构版本系统初始化完成
2025-07-25 13:05:30,773 - __main__ - INFO - 🚀 运行模式: 普通交易（无优化）
2025-07-25 13:05:30,773 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-25 13:05:30,773 - __main__ - INFO - 运行完整评估...
2025-07-25 13:05:30,773 - __main__ - INFO - 开始运行评估流程（新架构）
2025-07-25 13:05:30,773 - __main__ - INFO - 使用新架构创建智能体
2025-07-25 13:05:30,773 - contribution_assessment.infrastructure.configuration_manager - DEBUG - Updated config with 41 items
2025-07-25 13:05:30,773 - contribution_assessment.infrastructure.service_factory - DEBUG - Configured services with 41 configuration items
2025-07-25 13:05:30,795 - __main__ - INFO - OPRO智能体 NAA 初始化完成 (OPRO: 禁用)
2025-07-25 13:05:30,795 - __main__ - INFO - 创建智能体: NAA (OPRO: 禁用)
2025-07-25 13:05:30,795 - __main__ - INFO - ✅ 智能体 NAA 创建成功
2025-07-25 13:05:30,795 - __main__ - INFO - OPRO智能体 TAA 初始化完成 (OPRO: 禁用)
2025-07-25 13:05:30,795 - __main__ - INFO - 创建智能体: TAA (OPRO: 禁用)
2025-07-25 13:05:30,795 - __main__ - INFO - ✅ 智能体 TAA 创建成功
2025-07-25 13:05:30,795 - __main__ - INFO - OPRO智能体 FAA 初始化完成 (OPRO: 禁用)
2025-07-25 13:05:30,795 - __main__ - INFO - 创建智能体: FAA (OPRO: 禁用)
2025-07-25 13:05:30,796 - __main__ - INFO - ✅ 智能体 FAA 创建成功
2025-07-25 13:05:30,796 - __main__ - INFO - OPRO智能体 BOA 初始化完成 (OPRO: 禁用)
2025-07-25 13:05:30,796 - __main__ - INFO - 创建智能体: BOA (OPRO: 禁用)
2025-07-25 13:05:30,796 - __main__ - INFO - ✅ 智能体 BOA 创建成功
2025-07-25 13:05:30,796 - __main__ - INFO - OPRO智能体 BeOA 初始化完成 (OPRO: 禁用)
2025-07-25 13:05:30,796 - __main__ - INFO - 创建智能体: BeOA (OPRO: 禁用)
2025-07-25 13:05:30,796 - __main__ - INFO - ✅ 智能体 BeOA 创建成功
2025-07-25 13:05:30,796 - __main__ - INFO - OPRO智能体 NOA 初始化完成 (OPRO: 禁用)
2025-07-25 13:05:30,796 - __main__ - INFO - 创建智能体: NOA (OPRO: 禁用)
2025-07-25 13:05:30,796 - __main__ - INFO - ✅ 智能体 NOA 创建成功
2025-07-25 13:05:30,796 - __main__ - INFO - OPRO智能体 TRA 初始化完成 (OPRO: 禁用)
2025-07-25 13:05:30,796 - __main__ - INFO - 创建智能体: TRA (OPRO: 禁用)
2025-07-25 13:05:30,796 - __main__ - INFO - ✅ 智能体 TRA 创建成功
2025-07-25 13:05:30,796 - __main__ - INFO - 智能体创建完成，成功: 7/7
2025-07-25 13:05:30,796 - __main__ - INFO - ✅ 新架构成功创建智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-25 13:05:30,796 - __main__ - INFO - Starting assessment workflow workflow_req_20250725_130530
2025-07-25 13:05:30,796 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: workflow_started from phase_coordinator
2025-07-25 13:05:30,798 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: state_changed from None
2025-07-25 13:05:30,798 - contribution_assessment.services.state_manager - DEBUG - State saved: assessment/workflow_req_20250725_130530
2025-07-25 13:05:30,798 - __main__ - INFO - Starting coalition_generation phase for workflow workflow_req_20250725_130530
2025-07-25 13:05:30,798 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: phase_started from phase_coordinator
2025-07-25 13:05:30,798 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: coalition_generation_started from CoalitionService
2025-07-25 13:05:30,798 - __main__ - INFO - 开始联盟生成: 目标智能体=7, 分析智能体=3, 交易智能体=TRA
2025-07-25 13:05:30,798 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 开始联盟生成和剪枝
2025-07-25 13:05:30,798 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 总智能体数: 7
2025-07-25 13:05:30,798 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 分析智能体: {'FAA', 'TAA', 'NAA'}
2025-07-25 13:05:30,798 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 交易智能体: TRA
2025-07-25 13:05:30,798 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 生成了 128 个初始联盟
2025-07-25 13:05:30,799 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: coalition_generation_completed from CoalitionService
2025-07-25 13:05:30,799 - __main__ - INFO - 联盟生成完成: 有效联盟=56, 剪枝联盟=72, 耗时=0.000s
2025-07-25 13:05:30,799 - __main__ - INFO - Coalition generation completed in 0.00s, generated 56 coalitions
2025-07-25 13:05:30,799 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: phase_completed from phase_coordinator
2025-07-25 13:05:30,799 - __main__ - INFO - Starting trading_simulation phase for workflow workflow_req_20250725_130530
2025-07-25 13:05:30,799 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: phase_started from phase_coordinator
2025-07-25 13:05:30,799 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: batch_simulation_started from SimulationService
2025-07-25 13:05:30,799 - __main__ - INFO - 开始批量模拟: 56 个联盟，最大并发数: 60
2025-07-25 13:05:30,799 - __main__ - INFO - 🚀 开始分阶段交易模拟...
2025-07-25 13:05:30,799 - __main__ - INFO - 📊 模拟计划:
2025-07-25 13:05:30,799 - __main__ - INFO -   - 完整联盟: {'BeOA', 'FAA', 'TAA', 'BOA', 'TRA', 'NOA', 'NAA'} (详细日志)
2025-07-25 13:05:30,799 - __main__ - INFO -   - 子集联盟: 55 个 (简洁日志)
2025-07-25 13:05:30,799 - __main__ - INFO - ======================================================================
2025-07-25 13:05:30,799 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-25 13:05:30,799 - __main__ - INFO - ======================================================================
2025-07-25 13:05:30,799 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-25 13:05:30,799 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: simulation_started from SimulationService
2025-07-25 13:05:30,799 - __main__ - INFO - 开始模拟联盟: {'BeOA', 'FAA', 'TAA', 'BOA', 'TRA', 'NOA', 'NAA'}
2025-07-25 13:05:30,799 - __main__ - INFO - ⚠️ 未指定当前周数，使用默认值: 1
2025-07-25 13:05:30,799 - __main__ - INFO - 开始联盟交易模拟: frozenset({'BeOA', 'FAA', 'TAA', 'BOA', 'TRA', 'NOA', 'NAA'})
2025-07-25 13:05:30,800 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=False
2025-07-25 13:05:30,870 - __main__ - INFO - 📅 设置当前模拟周数: 1 (来自参数)
2025-07-25 13:05:30,872 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-25 13:05:30,872 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-25 13:05:30,872 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 数据: 新闻22条
2025-07-25 13:05:55,021 - __main__ - DEBUG - NAA 记录IO数据: 上日收益率=0.000000, 记录总数=1
2025-07-25 13:05:55,021 - __main__ - INFO - 🤖 NAA 输出: 5 个字段
2025-07-25 13:05:55,021 - __main__ - INFO - 🤖 执行智能体 TAA...
2025-07-25 13:05:55,021 - __main__ - INFO - 🤖 TAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 数据: 价格30个点
2025-07-25 13:06:25,785 - __main__ - DEBUG - TAA 记录IO数据: 上日收益率=0.000000, 记录总数=1
2025-07-25 13:06:25,786 - __main__ - INFO - 🤖 TAA 输出: 6 个字段
2025-07-25 13:06:25,786 - __main__ - INFO - 🤖 执行智能体 FAA...
2025-07-25 13:06:25,786 - __main__ - INFO - 🤖 FAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 数据: 基本面1个股票
2025-07-25 13:06:57,388 - __main__ - DEBUG - FAA 记录IO数据: 上日收益率=0.000000, 记录总数=1
2025-07-25 13:06:57,389 - __main__ - INFO - 🤖 FAA 输出: 13 个字段
2025-07-25 13:06:57,389 - __main__ - INFO - ✅ 分析层完成: 3 个智能体
2025-07-25 13:06:57,389 - __main__ - INFO - 🤖 执行展望层智能体 BOA...
2025-07-25 13:06:57,389 - __main__ - INFO - 🤖 BOA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000
