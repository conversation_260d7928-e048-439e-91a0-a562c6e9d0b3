#!/usr/bin/env python3
"""
测试收益率修复效果
"""

from stock_trading_env import StockTradingEnv
import pandas as pd
from datetime import datetime, timedelta

# 创建简单的测试数据
start_date = "2025-01-01"
end_date = "2025-01-05"

# 创建模拟价格数据
dates = pd.date_range(start_date, end_date, freq='D')
price_data = {
    'AAPL': pd.DataFrame({
        'date': dates,
        'open': [150.0, 151.0, 149.0, 152.0, 153.0],
        'high': [151.0, 152.0, 150.0, 153.0, 154.0], 
        'low': [149.0, 150.0, 148.0, 151.0, 152.0],
        'close': [150.5, 151.5, 149.5, 152.5, 153.5],
        'volume': [1000000] * 5
    })
}

# 创建交易环境
config = {
    'stocks': ['AAPL'],
    'start_date': start_date,
    'end_date': end_date,
    'starting_cash': 1000000,
    'price_data': price_data,
    'news_data': {'AAPL': []},
    'fundamental_data': {'AAPL': {}},
    'trading_days_per_week': 5
}

env = StockTradingEnv(config)

print("=== 收益率修复测试 ===")
print(f"初始状态: 现金={env.starting_cash}, 累计收益率={env.cumulative_return:.6f}")

# 第一天：买入操作
state, reward, done, info = env.step({'AAPL': 1.0})  # 全仓买入
print(f"第1天买入后: 累计收益率={env.cumulative_return:.6f}, 持仓={env.positions.get('AAPL', 0)}")

# 第二天：持有（应该显示收益率变化）
state, reward, done, info = env.step({'__HOLD__': 1.0})
print(f"第2天持有后: 累计收益率={env.cumulative_return:.6f}, 日收益率={info.get('daily_return', 0):.6f}")

# 第三天：继续持有
state, reward, done, info = env.step({'__HOLD__': 1.0})
print(f"第3天持有后: 累计收益率={env.cumulative_return:.6f}, 日收益率={info.get('daily_return', 0):.6f}")

print("\n=== 验证结果 ===")
if env.cumulative_return != 0.0:
    print("✅ 修复成功！收益率不再为0")
    print(f"实际累计收益率: {env.cumulative_return:.6f}")
else:
    print("❌ 修复失败，收益率仍为0")

print(f"最终净值: ${env.net_worth:.2f}")
print(f"预期净值变化: {(env.net_worth / env.starting_cash - 1) * 100:.2f}%")