# Design Document

## Overview

本设计文档旨在解决系统在运行到第二周时重复执行第一周交易模拟的问题。通过分析代码，我们发现问题出现在交易模拟器的`_is_week_end`方法和周期边界的处理逻辑中。本文档将详细说明问题的根本原因和解决方案。

## Architecture

系统的周期性优化架构包括以下几个关键组件：

1. **WeeklyCycleManager**: 负责管理周期性优化的整体流程
2. **PhaseCoordinator**: 负责协调各个阶段的执行
3. **TradingSimulator**: 负责执行交易模拟
4. **SimulationService**: 负责管理模拟服务

问题出现在TradingSimulator的`_is_week_end`方法中，该方法用于判断当前交易日是否为一周的最后一个交易日。当前实现为：

```python
def _is_week_end(self, day: int) -> bool:
    """
    判断是否为一周的最后一个交易日
    
    参数:
        day: 当前交易日（从0开始）
        
    返回:
        是否为周末
    """
    return (day + 1) % self.trading_days_per_week == 0
```

这个实现在第一周工作正常，但在第二周时，由于没有考虑当前周数，导致系统重复执行第一周的交易模拟。

## Components and Interfaces

### 1. TradingSimulator

TradingSimulator负责执行交易模拟，其中`run_simulation_for_coalition`方法是核心方法，用于为指定联盟运行交易模拟。该方法接受一个`current_week_number`参数，但在实际执行中没有正确使用这个参数来区分不同周的交易。

问题在于`_is_week_end`方法没有考虑当前周数，导致在第二周时仍然按照第一周的逻辑判断周末。

### 2. SimulationService

SimulationService负责管理模拟服务，其中`_run_phased_simulation`方法用于分阶段运行模拟。该方法在执行时没有正确传递当前周数给TradingSimulator，导致TradingSimulator无法区分不同周的交易。

### 3. WeeklyCycleManager

WeeklyCycleManager负责管理周期性优化的整体流程，其中`_execute_single_week_with_phase_coordinator`方法用于执行单周优化。该方法在执行时没有正确传递当前周数给PhaseCoordinator，导致PhaseCoordinator无法区分不同周的交易。

## Data Models

系统中涉及的主要数据模型包括：

1. **WeeklyResult**: 表示单周执行结果
2. **CycleResult**: 表示多周期执行结果
3. **SimulationResult**: 表示模拟结果

这些数据模型本身没有问题，但在数据传递过程中，当前周数没有被正确传递和使用。

## Error Handling

系统的错误处理机制已经比较完善，但在周期边界处理方面缺乏足够的检查和验证。我们需要增加对周期边界的检查，确保系统能够正确处理周期边界。

## Testing Strategy

为了验证修复是否有效，我们将采用以下测试策略：

1. **单元测试**: 测试修改后的`_is_week_end`方法在不同周数下的行为
2. **集成测试**: 测试整个周期性优化流程，确保系统能够正确处理周期边界
3. **端到端测试**: 运行完整的系统，验证系统能够正确执行多周期优化

## 解决方案

### 1. 修改TradingSimulator的`_is_week_end`方法

当前的`_is_week_end`方法只考虑了交易日，没有考虑当前周数。我们需要修改该方法，使其能够正确处理不同周的交易日。

```python
def _is_week_end(self, day: int) -> bool:
    """
    判断是否为一周的最后一个交易日
    
    参数:
        day: 当前交易日（从0开始）
        
    返回:
        是否为周末
    """
    # 计算当前周内的交易日（考虑周数）
    current_week = day // self.trading_days_per_week
    day_in_week = day % self.trading_days_per_week
    
    # 判断是否为当前周的最后一个交易日
    return day_in_week == self.trading_days_per_week - 1
```

### 2. 修改TradingSimulator的`run_simulation_for_coalition`方法

当前的`run_simulation_for_coalition`方法接受一个`current_week_number`参数，但没有正确使用这个参数。我们需要修改该方法，使其能够正确使用当前周数。

```python
def run_simulation_for_coalition(self,
                               coalition: Union[Set[str], List[str]],
                               agents: Dict[str, Any],
                               simulation_days: Optional[int] = None,
                               current_week_number: Optional[int] = None,
                               stop_after_one_week: bool = False) -> Dict[str, Any]:
    # ...
    
    # 设置当前模拟周数
    self.current_simulation_week = current_week_number if current_week_number is not None else 1
    
    # ...
    
    # 运行交易模拟
    for day in range(env.total_days - 1):
        # ...
        
        # 检查是否完成一周的交易
        if self._is_week_end(day):
            # ...
```

### 3. 确保正确传递当前周数

在SimulationService和WeeklyCycleManager中，我们需要确保正确传递当前周数给TradingSimulator。

```python
# 在SimulationService的_run_phased_simulation方法中
simulation_result = self.trading_simulator.run_simulation_for_coalition(
    coalition,
    agents,
    simulation_days,
    current_week_number=config.get("current_week_number", 1),  # 传递当前周数
    stop_after_one_week=True  # 启用5日循环模式
)

# 在WeeklyCycleManager的_execute_single_week_with_phase_coordinator方法中
assessment_request = AssessmentRequest(
    request_id=f"weekly_assessment_{week_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
    agents=agents,
    target_agents=target_agents or [],
    max_coalitions=max_coalitions or 50,
    enable_opro=weekly_config.get("enable_opro", True) if weekly_config else True,
    config={
        **self.config,
        "current_week_number": week_number  # 添加当前周数
    },
    opro_config=weekly_config.get("opro_config", {}) if weekly_config else {},
    max_concurrent_api_calls=weekly_config.get("max_concurrent_api_calls", 3) if weekly_config else 3
)
```

### 4. 增加日志记录

为了便于调试和监控，我们需要增加对周期边界处理的日志记录。

```python
# 在TradingSimulator的_is_week_end方法中
def _is_week_end(self, day: int) -> bool:
    """
    判断是否为一周的最后一个交易日
    
    参数:
        day: 当前交易日（从0开始）
        
    返回:
        是否为周末
    """
    # 计算当前周内的交易日（考虑周数）
    current_week = day // self.trading_days_per_week
    day_in_week = day % self.trading_days_per_week
    
    # 判断是否为当前周的最后一个交易日
    is_week_end = day_in_week == self.trading_days_per_week - 1
    
    if is_week_end and self.detailed_logging:
        self.logger.info(f"📅 检测到周末: 交易日={day}, 周数={current_week+1}, 周内交易日={day_in_week+1}")
    
    return is_week_end
```

## 总结

通过以上修改，我们可以解决系统在运行到第二周时重复执行第一周交易模拟的问题。主要修改包括：

1. 修改TradingSimulator的`_is_week_end`方法，使其能够正确处理不同周的交易日
2. 修改TradingSimulator的`run_simulation_for_coalition`方法，使其能够正确使用当前周数
3. 确保在SimulationService和WeeklyCycleManager中正确传递当前周数
4. 增加对周期边界处理的日志记录

这些修改将确保系统能够正确处理周期边界，避免重复执行交易模拟。