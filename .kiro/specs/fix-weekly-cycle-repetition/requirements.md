# Requirements Document

## Introduction

当前系统在运行到第二周时，仍然重复第一周的交易和子集交易。这个问题出现在`run_opro_system_new.py`中，当系统运行到第二周时，没有正确地处理周期边界，导致重复执行第一周的交易模拟。本需求文档旨在解决这个问题，确保系统能够正确地处理周期边界，避免重复执行交易模拟。

## Requirements

### Requirement 1

**User Story:** 作为系统管理员，我希望系统能够正确处理周期边界，避免在第二周重复执行第一周的交易模拟，以确保系统能够正确地进行周期性优化。

#### Acceptance Criteria

1. WHEN 系统运行到第二周 THEN 系统应该使用第二周的数据进行交易模拟，而不是重复使用第一周的数据
2. WHEN 系统执行周期性优化 THEN 系统应该正确地识别当前周数，并使用对应周的数据进行优化
3. WHEN 系统执行交易模拟 THEN 系统应该正确地传递当前周数给交易模拟器
4. WHEN 交易模拟器执行`_is_week_end`方法 THEN 该方法应该正确地识别周末，避免错误地触发周期性评估

### Requirement 2

**User Story:** 作为系统开发者，我希望能够清晰地了解周期边界的处理逻辑，以便在未来的开发中避免类似问题。

#### Acceptance Criteria

1. WHEN 系统处理周期边界 THEN 系统应该记录详细的日志，包括当前周数、交易日等信息
2. WHEN 系统执行周期性优化 THEN 系统应该记录优化的详细信息，包括优化前后的性能指标
3. WHEN 系统出现异常情况 THEN 系统应该记录详细的错误信息，便于调试和修复

### Requirement 3

**User Story:** 作为系统用户，我希望系统能够提供准确的周期性优化结果，以便我能够正确地评估智能体的性能。

#### Acceptance Criteria

1. WHEN 系统执行周期性优化 THEN 系统应该正确地计算每个智能体的Shapley值
2. WHEN 系统生成优化报告 THEN 报告应该包含准确的周期信息，避免混淆不同周期的数据
3. WHEN 系统执行多周期优化 THEN 系统应该能够正确地累积和比较不同周期的性能指标