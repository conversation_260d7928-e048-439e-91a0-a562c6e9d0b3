# Implementation Plan

- [x] 1. 修改TradingSimulator的_is_week_end方法
  - 更新方法实现，考虑当前周数和周内交易日
  - 添加详细日志记录，便于调试和监控
  - _Requirements: 1.1, 1.4, 2.1_

- [x] 2. 修改TradingSimulator的run_simulation_for_coalition方法
  - [x] 2.1 正确设置和使用current_simulation_week属性
    - 确保方法正确使用传入的current_week_number参数
    - 添加日志记录，显示当前模拟周数
    - _Requirements: 1.2, 1.3, 2.1_
  
  - [x] 2.2 优化周期边界处理逻辑
    - 确保在周末正确处理周期数据
    - 避免重复执行相同周的交易模拟
    - _Requirements: 1.1, 1.2, 3.1_

- [x] 3. 修改SimulationService的_run_phased_simulation方法
  - 确保正确传递当前周数给TradingSimulator
  - 添加日志记录，显示当前模拟周数和阶段
  - _Requirements: 1.3, 2.1, 2.2_

- [x] 4. 修改WeeklyCycleManager的_execute_single_week_with_phase_coordinator方法
  - 在AssessmentRequest中添加当前周数
  - 确保正确传递周数给下游组件
  - _Requirements: 1.2, 1.3, 3.2_

- [x] 5. 添加单元测试验证修复
  - [x] 5.1 测试修改后的_is_week_end方法
    - 测试不同周数和交易日的组合
    - 验证方法返回的结果是否正确
    - _Requirements: 1.4, 2.3_
  
  - [x] 5.2 测试周期边界处理逻辑
    - 模拟多周期执行，验证是否正确处理周期边界
    - 检查是否避免了重复执行交易模拟
    - _Requirements: 1.1, 1.2, 3.3_

- [x] 6. 更新文档和注释
  - 更新方法注释，说明周期边界处理逻辑
  - 添加详细的实现说明，便于未来维护
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 7. 修复交易日期选择逻辑
  - [ ] 7.1 修改StockTradingEnv的初始化逻辑
    - 根据当前周数调整交易起始日期
    - 确保第二周从正确的日期开始交易
    - _Requirements: 1.1, 1.2_
  
  - [ ] 7.2 在TradingSimulator中添加日期调整逻辑
    - 根据current_week_number调整交易环境的起始日期
    - 确保不同周使用不同的交易日期范围
    - _Requirements: 1.1, 1.2, 3.1_