# 设计文档

## 概述

本设计旨在实现缺失的OPRO优化器组件，该组件是系统中基于贡献度的智能体提示词优化功能的核心。OPRO优化器将实现Contribution-Guided Online Prompt Optimization (CG-OPO)算法，根据DAG-Shapley值识别表现较差的智能体并生成改进的提示词。

## 架构

### 系统集成架构

```
┌─────────────────────────────────────────────────────────────┐
│                    OPRO优化服务层                            │
├─────────────────────────────────────────────────────────────┤
│  OPROService (已存在)                                       │
│  ├── optimize_agents()                                      │
│  ├── optimize_single_agent()                               │
│  └── evaluate_optimization()                               │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   OPRO优化器核心                            │
├─────────────────────────────────────────────────────────────┤
│  OPROOptimizer (待实现)                                     │
│  ├── optimize_all_agents()                                 │
│  ├── optimize_single_agent()                               │
│  ├── generate_improved_prompt()                            │
│  └── evaluate_prompt_quality()                             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    智能体层                                 │
├─────────────────────────────────────────────────────────────┤
│  OPROBaseAgent (已存在)                                     │
│  ├── get_prompt_template()                                 │
│  ├── update_prompt()                                       │
│  └── record_performance()                                  │
└─────────────────────────────────────────────────────────────┘
```

### 数据流架构

```
Shapley值 → 识别最差智能体 → 提取当前提示词 → 生成改进提示词 → 验证质量 → 更新智能体
    ↑                                                                    │
    └────────────────── 性能反馈循环 ←──────────────────────────────────────┘
```

## 组件和接口

### 核心组件：OPROOptimizer

```python
class OPROOptimizer:
    """
    OPRO优化器核心实现
    
    负责基于贡献度的智能体提示词优化，实现CG-OPO算法
    """
    
    def __init__(self, 
                 llm_interface: LLMInterface,
                 config: Dict[str, Any] = None,
                 logger: Optional[logging.Logger] = None):
        """初始化优化器"""
        
    def optimize_all_agents(self, 
                          agent_ids: List[str], 
                          current_prompts: Dict[str, str]) -> Dict[str, Any]:
        """批量优化智能体"""
        
    def optimize_single_agent(self, 
                            agent_name: str, 
                            current_prompt: str, 
                            performance_score: float, 
                            failure_cases: List[Dict] = None) -> Dict[str, Any]:
        """优化单个智能体"""
        
    def generate_improved_prompt(self, 
                               agent_type: str, 
                               current_prompt: str, 
                               performance_issues: List[str]) -> str:
        """生成改进的提示词"""
        
    def evaluate_prompt_quality(self, 
                              prompt: str, 
                              agent_type: str) -> Dict[str, Any]:
        """评估提示词质量"""
```

### 提示词优化策略

#### 智能体类型特化策略

1. **分析层智能体 (NAA, TAA, FAA)**
   - 专注于数据处理准确性
   - 强化专业领域知识
   - 改进输出格式一致性

2. **展望层智能体 (BOA, BeOA, NOA)**
   - 增强综合分析能力
   - 优化决策逻辑
   - 提高预测准确性

3. **决策层智能体 (TRA)**
   - 强化风险管理
   - 优化交易决策逻辑
   - 提高一致性验证

#### 优化模板系统

```python
OPTIMIZATION_TEMPLATES = {
    "performance_improvement": """
    基于以下性能问题改进提示词：
    当前提示词：{current_prompt}
    性能问题：{performance_issues}
    智能体类型：{agent_type}
    
    请生成改进的提示词，重点解决以下问题：
    {specific_improvements}
    """,
    
    "consistency_enhancement": """
    提高提示词的一致性和可靠性：
    当前提示词：{current_prompt}
    一致性问题：{consistency_issues}
    
    请优化提示词以确保输出格式和逻辑的一致性。
    """,
    
    "domain_specialization": """
    针对{agent_type}智能体的专业领域优化：
    当前提示词：{current_prompt}
    专业要求：{domain_requirements}
    
    请增强提示词的专业性和准确性。
    """
}
```

## 数据模型

### 优化配置模型

```python
@dataclass
class OptimizationConfig:
    """优化配置"""
    temperature: float = 0.7
    max_iterations: int = 3
    improvement_threshold: float = 0.05
    quality_threshold: float = 0.7
    enable_validation: bool = True
    enable_rollback: bool = True
```

### 优化结果模型

```python
@dataclass
class OptimizationResult:
    """优化结果"""
    success: bool
    agent_id: str
    original_prompt: str
    optimized_prompt: str
    estimated_improvement: float
    quality_score: float
    optimization_applied: bool
    metadata: Dict[str, Any]
```

### 性能问题分析模型

```python
@dataclass
class PerformanceIssue:
    """性能问题"""
    issue_type: str  # "accuracy", "consistency", "format", "logic"
    severity: float  # 0.0-1.0
    description: str
    suggested_fix: str
```

## 错误处理

### 异常层次结构

```python
class OPROOptimizationError(Exception):
    """OPRO优化基础异常"""
    pass

class PromptGenerationError(OPROOptimizationError):
    """提示词生成异常"""
    pass

class QualityValidationError(OPROOptimizationError):
    """质量验证异常"""
    pass

class OptimizationTimeoutError(OPROOptimizationError):
    """优化超时异常"""
    pass
```

### 错误处理策略

1. **优雅降级**：优化失败时直接报错,停止
2. **重试机制**：网络或临时错误时自动重试
3. **回滚支持**：质量验证失败时报错,停止
4. **详细日志**：记录所有优化过程和错误信息

## 测试策略

### 单元测试覆盖

1. **提示词生成测试**
   - 测试不同智能体类型的提示词生成
   - 验证生成提示词的格式和内容
   - 测试边界条件和异常情况

2. **质量评估测试**
   - 测试提示词质量评分算法
   - 验证质量阈值判断逻辑
   - 测试不同质量水平的提示词

3. **优化流程测试**
   - 测试完整的优化流程
   - 验证批量优化功能
   - 测试优化结果的正确性

### 集成测试策略

1. **与OPROService集成测试**
   - 验证服务接口调用
   - 测试配置传递和结果返回
   - 验证异常处理机制

2. **与智能体集成测试**
   - 测试提示词更新功能
   - 验证性能记录机制

3. **端到端测试**
   - 模拟完整的优化周期
   - 验证系统稳定性
   - 测试性能改进效果

### 性能测试

1. **优化速度测试**
   - 单个智能体优化时间
   - 批量优化性能
   - 并发优化能力

2. **内存使用测试**
   - 优化过程内存占用
   - 历史数据存储效率
   - 内存泄漏检测

## 实现细节

### 提示词改进算法

1. **问题识别**
   - 基于Shapley值识别性能问题
   - 分析历史失败案例
   - 提取关键改进点

2. **改进生成**
   - 使用LLM生成改进建议
   - 应用智能体类型特化模板
   - 保持原有功能完整性

3. **质量验证**
   - 语法和格式检查
   - 逻辑一致性验证
   - 专业性评估

### 配置管理

```python
DEFAULT_CONFIG = {
    "optimization": {
        "temperature": 0.7,
        "max_iterations": 3,
        "improvement_threshold": 0.05,
        "quality_threshold": 0.7,
        "timeout_seconds": 300
    },
    "validation": {
        "enable_syntax_check": True,
        "enable_logic_check": True,
        "enable_format_check": True,
        "min_prompt_length": 50,
        "max_prompt_length": 5000
    },
    "agent_specialization": {
        "NAA": {"focus": "news_analysis", "keywords": ["新闻", "情绪", "舆情"]},
        "TAA": {"focus": "technical_analysis", "keywords": ["技术", "趋势", "指标"]},
        "FAA": {"focus": "fundamental_analysis", "keywords": ["基本面", "财务", "估值"]},
        "BOA": {"focus": "bullish_outlook", "keywords": ["看涨", "乐观", "买入"]},
        "BeOA": {"focus": "bearish_outlook", "keywords": ["看跌", "悲观", "卖出"]},
        "NOA": {"focus": "neutral_outlook", "keywords": ["中性", "平衡", "观察"]},
        "TRA": {"focus": "trading_decision", "keywords": ["交易", "决策", "风险"]}
    }
}
```

### 日志和监控

1. **优化过程日志**
   - 记录每次优化的详细过程
   - 包含输入参数和输出结果
   - 记录性能改进情况

2. **性能监控**
   - 跟踪优化成功率
   - 监控平均改进幅度
   - 记录异常和错误统计

3. **调试支持**
   - 详细的错误堆栈信息
   - 中间结果保存
   - 可配置的日志级别

## 安全考虑

1. **提示词安全**
   - 防止恶意提示词注入
   - 验证生成内容的安全性
   - 限制提示词长度和复杂度

2. **API安全**
   - LLM API密钥安全管理
   - 请求频率限制
   - 错误信息脱敏

3. **数据隐私**
   - 敏感信息过滤
   - 历史数据加密存储
   - 访问权限控制