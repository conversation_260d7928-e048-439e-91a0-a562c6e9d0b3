2025-07-25 19:49:24,213 - __main__ - INFO - ====================================================================================================
2025-07-25 19:49:24,213 - __main__ - INFO - 🚀 OPRO系统启动（重构版本）
2025-07-25 19:49:24,213 - __main__ - INFO - ====================================================================================================
2025-07-25 19:49:24,213 - __main__ - INFO - 运行模式: weekly
2025-07-25 19:49:24,213 - __main__ - INFO - LLM提供商: zhipuai
2025-07-25 19:49:24,213 - __main__ - INFO - 系统架构: 重构版本（Service-Based Architecture）
2025-07-25 19:49:24,213 - __main__ - INFO - OPRO启用: True
2025-07-25 19:49:24,213 - __main__ - INFO - 📊 投资组合跟踪器: 启用（记录每日投资组合状态）
2025-07-25 19:49:24,213 - __main__ - INFO - 💾 持久化路径: 自动生成
2025-07-25 19:49:24,213 - __main__ - INFO - 🔄 跨周状态继承: 启用（支持周间状态传递）
2025-07-25 19:49:24,214 - __main__ - INFO - ✅ 完整模式: 跟踪器=True, 继承状态=True
2025-07-25 19:49:24,214 - __main__ - INFO - 周期性优化启用: 每 7 天
2025-07-25 19:49:24,215 - __main__ - INFO - 初始化重构版本系统...
2025-07-25 19:49:24,326 - __main__ - INFO - 🔧 使用ServiceFactory创建重构版本的评估器...
2025-07-25 19:49:24,326 - __main__ - INFO - 初始化RefactoredContributionAssessor组件...
2025-07-25 19:49:24,328 - contribution_assessment.shapley_calculator.ShapleyCalculator - INFO - Shapley值计算器初始化完成
2025-07-25 19:49:24,329 - state_management.daily_state_manager - INFO - DailyStateManager initialized with storage: data/state_manager.db
2025-07-25 19:49:24,348 - state_management.daily_state_manager - INFO - DailyStateManager initialized with storage: data/state_manager.db
2025-07-25 19:49:24,348 - __main__ - INFO - 联盟服务初始化完成
2025-07-25 19:49:24,348 - __main__ - INFO - 模拟服务初始化完成
2025-07-25 19:49:24,348 - __main__ - INFO - 简化OPRO服务初始化完成 (with historical data support)
2025-07-25 19:49:24,348 - contribution_assessment.services.state_manager - INFO - StateManager initialized with storage: data/state_manager.db
2025-07-25 19:49:24,348 - __main__ - INFO - ✅ RefactoredContributionAssessor初始化完成
2025-07-25 19:49:24,348 - __main__ - INFO - ✅ 重构版本的评估器创建成功
2025-07-25 19:49:24,348 - __main__ - INFO - ✅ 重构版本系统初始化完成
2025-07-25 19:49:24,348 - __main__ - INFO - 🔄 使用周期性优化模式
2025-07-25 19:49:24,348 - __main__ - INFO - 🔄 运行模式: 周期性在线优化（新架构WeeklyCycleManager）
2025-07-25 19:49:24,349 - __main__ - INFO - 📋 架构特点: 周 > 阶段 的层级结构
2025-07-25 19:49:24,349 - __main__ - INFO - 📋 周期性优化配置:
2025-07-25 19:49:24,349 - __main__ - INFO -    优化频率: 每 7 交易日
2025-07-25 19:49:24,349 - __main__ - INFO -    最少运行天数: 5 天
2025-07-25 19:49:24,349 - __main__ - INFO -    每周期最多优化: 1 个智能体
2025-07-25 19:49:24,349 - __main__ - INFO -    容错模式: 启用
2025-07-25 19:49:24,349 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-25 19:49:24,349 - __main__ - INFO - 🚀 使用WeeklyCycleManager架构（周 > 阶段）
2025-07-25 19:49:24,350 - __main__ - INFO - 📅 日期范围: 2025-01-01 到 2025-02-15
2025-07-25 19:49:24,350 - __main__ - INFO - 📊 交易日总数: 33 天
2025-07-25 19:49:24,351 - __main__ - INFO - 🔄 自动计算周数: 6 周 (每周5个交易日)
2025-07-25 19:49:24,358 - __main__ - INFO - 📊 启用日常投资组合跟踪器 (持久化: data/portfolio_tracker_20250725_194924.json)
2025-07-25 19:49:24,358 - __main__ - WARNING - ⚠️ 投资组合跟踪器未启用，将使用旧版状态传递机制
2025-07-25 19:49:24,377 - __main__ - INFO - ✅ 使用WeeklyOptimizationService作为优化执行器
2025-07-25 19:49:24,377 - __main__ - INFO - ✅ WeeklyCycleManager组件初始化成功
2025-07-25 19:49:24,377 - __main__ - INFO - ✅ WeeklyCycleManager创建成功，开始执行周期性优化
2025-07-25 19:49:24,377 - __main__ - INFO - 🚀 开始6周的优化循环
2025-07-25 19:49:24,377 - __main__ - INFO - 
📅 === 第1周优化循环 ===
2025-07-25 19:49:24,377 - __main__ - INFO - 🚀 开始第 1 周优化周期 (6阶段工作流)
2025-07-25 19:49:24,377 - __main__ - INFO - 创建智能体配置...
2025-07-25 19:49:24,377 - __main__ - INFO - 使用新架构创建智能体
2025-07-25 19:49:24,401 - __main__ - INFO - OPRO智能体 NAA 初始化完成 (OPRO: 启用)
2025-07-25 19:49:24,401 - __main__ - INFO - 创建智能体: NAA (OPRO: 启用)
2025-07-25 19:49:24,401 - __main__ - INFO - ✅ 智能体 NAA 创建成功
2025-07-25 19:49:24,401 - __main__ - INFO - OPRO智能体 TAA 初始化完成 (OPRO: 启用)
2025-07-25 19:49:24,401 - __main__ - INFO - 创建智能体: TAA (OPRO: 启用)
2025-07-25 19:49:24,401 - __main__ - INFO - ✅ 智能体 TAA 创建成功
2025-07-25 19:49:24,401 - __main__ - INFO - OPRO智能体 FAA 初始化完成 (OPRO: 启用)
2025-07-25 19:49:24,401 - __main__ - INFO - 创建智能体: FAA (OPRO: 启用)
2025-07-25 19:49:24,401 - __main__ - INFO - ✅ 智能体 FAA 创建成功
2025-07-25 19:49:24,401 - __main__ - INFO - OPRO智能体 BOA 初始化完成 (OPRO: 启用)
2025-07-25 19:49:24,401 - __main__ - INFO - 创建智能体: BOA (OPRO: 启用)
2025-07-25 19:49:24,401 - __main__ - INFO - ✅ 智能体 BOA 创建成功
2025-07-25 19:49:24,401 - __main__ - INFO - OPRO智能体 BeOA 初始化完成 (OPRO: 启用)
2025-07-25 19:49:24,401 - __main__ - INFO - 创建智能体: BeOA (OPRO: 启用)
2025-07-25 19:49:24,401 - __main__ - INFO - ✅ 智能体 BeOA 创建成功
2025-07-25 19:49:24,401 - __main__ - INFO - OPRO智能体 NOA 初始化完成 (OPRO: 启用)
2025-07-25 19:49:24,401 - __main__ - INFO - 创建智能体: NOA (OPRO: 启用)
2025-07-25 19:49:24,401 - __main__ - INFO - ✅ 智能体 NOA 创建成功
2025-07-25 19:49:24,401 - __main__ - INFO - OPRO智能体 TRA 初始化完成 (OPRO: 启用)
2025-07-25 19:49:24,402 - __main__ - INFO - 创建智能体: TRA (OPRO: 启用)
2025-07-25 19:49:24,402 - __main__ - INFO - ✅ 智能体 TRA 创建成功
2025-07-25 19:49:24,402 - __main__ - INFO - 智能体创建完成，成功: 7/7
2025-07-25 19:49:24,402 - __main__ - INFO - ✅ 新架构成功创建智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-25 19:49:24,402 - __main__ - INFO - 可用智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-25 19:49:24,402 - __main__ - INFO - 🔄 第1周使用旧版跨周状态传递
2025-07-25 19:49:24,402 - __main__ - INFO - 🔄 开始第 1 周评估周期 (6阶段工作流)
2025-07-25 19:49:24,402 - __main__ - INFO - 📊 Phase 1: 联盟生成与子集运算
2025-07-25 19:49:24,402 - __main__ - INFO - Starting coalition_generation phase for workflow weekly_workflow_weekly_assessment_1_20250725_194924_week_1
2025-07-25 19:49:24,402 - __main__ - INFO - 开始联盟生成: 目标智能体=7, 分析智能体=3, 交易智能体=TRA
2025-07-25 19:49:24,402 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 开始联盟生成和剪枝
2025-07-25 19:49:24,402 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 总智能体数: 7
2025-07-25 19:49:24,402 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 分析智能体: {'TAA', 'FAA', 'NAA'}
2025-07-25 19:49:24,402 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 交易智能体: TRA
2025-07-25 19:49:24,402 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 生成了 128 个初始联盟
2025-07-25 19:49:24,402 - __main__ - INFO - 限制联盟数量: 56 -> 50
2025-07-25 19:49:24,402 - __main__ - INFO - 联盟生成完成: 有效联盟=50, 剪枝联盟=72, 耗时=0.000s
2025-07-25 19:49:24,402 - __main__ - INFO - Coalition generation completed in 0.00s, generated 50 coalitions
2025-07-25 19:49:24,402 - __main__ - INFO - 💹 Phase 2: 交易模拟
2025-07-25 19:49:24,402 - __main__ - INFO - Starting trading_simulation phase for workflow weekly_workflow_weekly_assessment_1_20250725_194924_week_1
2025-07-25 19:49:24,402 - __main__ - INFO - 开始批量模拟: 50 个联盟，最大并发数: 60
2025-07-25 19:49:24,402 - __main__ - INFO - 🚀 开始分阶段交易模拟...
2025-07-25 19:49:24,402 - __main__ - INFO - 📊 模拟计划:
2025-07-25 19:49:24,402 - __main__ - INFO -   - 完整联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'} (详细日志)
2025-07-25 19:49:24,402 - __main__ - INFO -   - 子集联盟: 49 个 (简洁日志)
2025-07-25 19:49:24,402 - __main__ - INFO - ======================================================================
2025-07-25 19:49:24,402 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-25 19:49:24,402 - __main__ - INFO - ======================================================================
2025-07-25 19:49:24,402 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-25 19:49:24,402 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-25 19:49:24,402 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 19:49:24,402 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-25 19:49:24,403 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=False
2025-07-25 19:49:24,497 - __main__ - INFO - 📅 设置当前模拟周数: 1 (来自参数)
2025-07-25 19:49:24,498 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-25 19:49:24,498 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-25 19:49:24,498 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 数据: 新闻22条
2025-07-25 19:49:48,646 - __main__ - INFO - 🤖 NAA 输出: 5 个字段
2025-07-25 19:49:48,646 - __main__ - INFO - 🤖 执行智能体 TAA...
2025-07-25 19:49:48,647 - __main__ - INFO - 🤖 TAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 数据: 价格30个点
2025-07-25 19:51:02,207 - __main__ - INFO - 🤖 TAA 输出: 6 个字段
2025-07-25 19:51:02,208 - __main__ - INFO - 🤖 执行智能体 FAA...
2025-07-25 19:51:02,209 - __main__ - INFO - 🤖 FAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 数据: 基本面1个股票
2025-07-25 19:51:16,973 - __main__ - INFO - 🤖 FAA 输出: 13 个字段
2025-07-25 19:51:16,974 - __main__ - INFO - ✅ 分析层完成: 3 个智能体
2025-07-25 19:51:16,974 - __main__ - INFO - 🤖 执行展望层智能体 BOA...
2025-07-25 19:51:16,974 - __main__ - INFO - 🤖 BOA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000
2025-07-25 19:51:31,855 - __main__ - INFO - 🤖 BOA 输出: 5 个字段
2025-07-25 19:51:31,856 - __main__ - INFO - ✅ 展望层智能体 BOA 完成: 已输出结果
2025-07-25 19:51:31,856 - __main__ - INFO - 🤖 执行展望层智能体 BeOA...
2025-07-25 19:51:31,856 - __main__ - INFO - 🤖 BeOA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000
2025-07-25 19:52:43,838 - __main__ - INFO - 🤖 BeOA 输出: 5 个字段
2025-07-25 19:52:43,838 - __main__ - INFO - ✅ 展望层智能体 BeOA 完成: 已输出结果
2025-07-25 19:52:43,838 - __main__ - INFO - 🤖 执行展望层智能体 NOA...
2025-07-25 19:52:43,838 - __main__ - INFO - 🤖 NOA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000
2025-07-25 19:53:34,397 - __main__ - INFO - 🤖 NOA 输出: 5 个字段
2025-07-25 19:53:34,398 - __main__ - INFO - ✅ 展望层智能体 NOA 完成: 已输出结果
2025-07-25 19:53:34,398 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-25 19:53:34,398 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000
2025-07-25 19:53:34,398 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 3113
2025-07-25 19:53:34,398 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的观点输出,综合判断,进行决策**

...
2025-07-25 19:53:34,398 - __main__ - INFO - 🔍 提示词结尾200字符: ...n- **情绪评分**：0.6，表明市场情绪较为中性，但略偏向乐观。\n- **新闻摘要**：苹... (信心度: 未知)
  • NOA: {'analysis': '📅 分析日期: 2025-01-02\n\n🤖 分析层智能体输出分析：\n\n- **市场展望（中性）**：市场情绪评分0.6，显示出一定的乐观情绪，但并非全面看涨。科技巨... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 19:53:59,226 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-25 19:53:59,226 - __main__ - INFO - 🔍 LLM响应前200字符: {'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示市场情绪较为积极，但苹果公司在中国市场的降价策略和市场份额下降可能对股价产生负面影响。TAA显示短期趋势可能为看跌，技术指标也显示出一定的看跌信号。FAA显示苹果公司的估值合理，财务健康，竞争地位领先，长期趋势看好。展望层智能体中，BOA和NOA显示出一定的乐观情绪，而BeOA则显示出中性偏乐观的情绪...
2025-07-25 19:53:59,226 - __main__ - INFO - 🔍 解析后结果: {'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示市场情绪较为积极，但苹果公司在中国市场的降价策略和市场份额下降可能对股价产生负面影响。TAA显示短期趋势可能为看跌，技术指标也显示出一定的看跌信号。FAA显示苹果公司的估值合理，财务健康，竞争地位领先，长期趋势看好。展望层智能体中，BOA和NOA显示出一定的乐观情绪，而BeOA则显示出中性偏乐观的情绪。然而，由于BOA和BeOA的信号方向相反，我们优先考虑NOA的中性观望建议。因此，综合考虑所有因素，我们得出**最终决策：保持不变**。'}
2025-07-25 19:53:59,226 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-25 19:53:59,226 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-25 19:53:59,226 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-25 19:53:59,226 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-25 19:53:59,227 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-25 19:53:59,227 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示市场情绪较为积极，但苹果公司在中国市场的降价策略和市场份额下降可能对股价产生负面影响。TAA显示短期趋势可能为看跌，技术指标也显示出一定的看跌信号。FAA显示苹果公司的估值合理，财务健康，竞争地位领先，长期趋势看好。展望层智能体中，BOA和NOA显示出一定的乐观情绪，而BeOA则显示出中性偏乐观的情绪。然而，由于BOA和BeOA的信号方向相反，我们优先考虑NOA的中性观望建议。因此，综合考虑所有因素，我们得出**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-02'}
2025-07-25 19:53:59,227 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-25 19:53:59,231 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-25 19:53:59,231 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-25 19:53:59,231 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 数据: 新闻48条
2025-07-25 19:54:06,200 - __main__ - ERROR - LLM分析请求失败: Error code: 429, with error text {"error":{"code":"1302","message":"您当前使用该API的并发数过高，请降低并发，或联系客服增加限额。"}}
2025-07-25 19:54:06,201 - __main__ - WARNING - 🤖 NAA: 数据验证失败，使用默认响应
2025-07-25 19:54:06,201 - __main__ - ERROR - 智能体 NAA LLM调用失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-25 19:54:06,201 - __main__ - INFO - 🤖 NAA 输出: 6 个字段
2025-07-25 19:54:06,201 - __main__ - INFO - 🤖 执行智能体 TAA...
2025-07-25 19:54:06,201 - __main__ - INFO - 🤖 TAA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 数据: 价格30个点
2025-07-25 19:54:47,311 - __main__ - INFO - 🤖 TAA 输出: 6 个字段
2025-07-25 19:54:47,314 - __main__ - INFO - 🤖 执行智能体 FAA...
2025-07-25 19:54:47,314 - __main__ - INFO - 🤖 FAA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 数据: 基本面1个股票
2025-07-25 19:55:08,170 - __main__ - INFO - 🤖 FAA 输出: 13 个字段
2025-07-25 19:55:08,171 - __main__ - INFO - ✅ 分析层完成: 3 个智能体
2025-07-25 19:55:08,171 - __main__ - INFO - 🤖 执行展望层智能体 BOA...
2025-07-25 19:55:08,171 - __main__ - INFO - 🤖 BOA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000
2025-07-25 19:55:53,533 - __main__ - INFO - 🤖 BOA 输出: 5 个字段
2025-07-25 19:55:53,534 - __main__ - INFO - ✅ 展望层智能体 BOA 完成: 已输出结果
2025-07-25 19:55:53,534 - __main__ - INFO - 🤖 执行展望层智能体 BeOA...
2025-07-25 19:55:53,534 - __main__ - INFO - 🤖 BeOA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000
2025-07-25 19:56:11,843 - __main__ - INFO - 🤖 BeOA 输出: 5 个字段
2025-07-25 19:56:11,843 - __main__ - INFO - ✅ 展望层智能体 BeOA 完成: 已输出结果
2025-07-25 19:56:11,843 - __main__ - INFO - 🤖 执行展望层智能体 NOA...
2025-07-25 19:56:11,843 - __main__ - INFO - 🤖 NOA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000
2025-07-25 19:56:26,539 - __main__ - INFO - 🤖 NOA 输出: 5 个字段
2025-07-25 19:56:26,540 - __main__ - INFO - ✅ 展望层智能体 NOA 完成: 已输出结果
2025-07-25 19:56:26,540 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-25 19:56:26,540 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000
2025-07-25 19:56:26,541 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 2070
2025-07-25 19:56:26,541 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的观点输出,综合判断,进行决策**

...
2025-07-25 19:56:26,541 - __main__ - INFO - 🔍 提示词结尾200字符: ...分析，尽管技术面显示中性，但考虑到以下风险因素，我们持看跌展望。\n\n看跌因素列表：\n1. ... (信心度: 未知)
  • NOA: {'analysis': '分析日期: 2025-01-03\n\n市场展望（中性）：\n根据分析层智能体的输出，AAPL在2025-01-03的市场表现是中性的。没有明显的趋势，收盘价与开盘价相同，... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 19:56:32,964 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-25 19:56:32,964 - __main__ - INFO - 🔍 LLM响应前200字符: {'analysis': '在2025-01-03的分析中，我们首先考虑了分析层智能体的输出。NAA由于LLM不可用，使用了默认分析，其信心度为0.5。TAA显示AAPL在当天的技术面是中性的，RSI值接近50，MACD线接近零轴，5日和20日移动平均线接近，没有明显的趋势。FAA显示AAPL的估值合理，财务健康得分较高，竞争地位领先。展望层智能体中，BOA和BeOA的信号未知，但NOA认为AAP...
2025-07-25 19:56:32,964 - __main__ - INFO - 🔍 解析后结果: {'analysis': '在2025-01-03的分析中，我们首先考虑了分析层智能体的输出。NAA由于LLM不可用，使用了默认分析，其信心度为0.5。TAA显示AAPL在当天的技术面是中性的，RSI值接近50，MACD线接近零轴，5日和20日移动平均线接近，没有明显的趋势。FAA显示AAPL的估值合理，财务健康得分较高，竞争地位领先。展望层智能体中，BOA和BeOA的信号未知，但NOA认为AAPL在当天的市场表现是中性的。考虑到这些信息，我们得出结论，AAPL在2025-01-03没有明显的买入或卖出信号。因此，**最终决策：保持不变**。'}
2025-07-25 19:56:32,964 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-25 19:56:32,964 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-25 19:56:32,964 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-25 19:56:32,965 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-25 19:56:32,965 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-25 19:56:32,965 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在2025-01-03的分析中，我们首先考虑了分析层智能体的输出。NAA由于LLM不可用，使用了默认分析，其信心度为0.5。TAA显示AAPL在当天的技术面是中性的，RSI值接近50，MACD线接近零轴，5日和20日移动平均线接近，没有明显的趋势。FAA显示AAPL的估值合理，财务健康得分较高，竞争地位领先。展望层智能体中，BOA和BeOA的信号未知，但NOA认为AAPL在当天的市场表现是中性的。考虑到这些信息，我们得出结论，AAPL在2025-01-03没有明显的买入或卖出信号。因此，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-03'}
2025-07-25 19:56:32,965 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-25 19:56:32,971 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-25 19:56:32,971 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-25 19:56:32,971 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000, 数据: 新闻32条
