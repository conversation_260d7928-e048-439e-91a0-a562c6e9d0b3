# 周期性交易日期调整修复

## 问题描述

在运行到第二周时，系统仍然从第一周的起始日期（1月2日）开始交易，导致重复执行相同的交易模拟。这个问题出现在`TradingSimulator`的`_create_trading_environment`方法中，该方法没有考虑当前周数，导致不同周的交易使用相同的起始日期。

## 根本原因

经过分析，我们发现问题出在以下几个方面：

1. **交易环境创建时没有考虑当前周数**：`_create_trading_environment`方法在创建交易环境时，没有根据当前周数调整交易起始日期，导致不同周的交易使用相同的起始日期。

2. **周数传递不完整**：虽然系统中有`current_week_number`参数，但没有正确传递给`_create_trading_environment`方法，导致无法根据周数调整交易日期。

## 修复方案

我们通过以下几个方面的修改解决了这个问题：

1. **修改TradingSimulator的`_create_trading_environment`方法**：

   ```python
   def _create_trading_environment(self, simulation_days: Optional[int] = None, coalition_id: Optional[str] = None, current_week_number: Optional[int] = None) -> StockTradingEnv:
       # ...
       
       # 根据当前周数调整起始日期
       if current_week_number is not None and current_week_number > 1:
           # 计算每周的交易日数
           days_per_week = self.trading_days_per_week
           
           # 计算日历天数（考虑周末）
           calendar_days_per_week = int(days_per_week * 7 / 5)  # 假设每周5个交易日对应7个日历日
           
           # 调整起始日期
           adjusted_start_date = base_start_date + pd.Timedelta(days=calendar_days_per_week * (current_week_number - 1))
           
           if self.detailed_logging:
               self.logger.info(f"📅 调整交易起始日期: 从 {base_start_date.strftime('%Y-%m-%d')} 到 {adjusted_start_date.strftime('%Y-%m-%d')} (第{current_week_number}周)")
           
           # 更新配置
           env_config["start_date"] = adjusted_start_date.strftime("%Y-%m-%d")
           
           # 如果指定了模拟天数，也相应调整结束日期
           if simulation_days is not None:
               end_date = adjusted_start_date + pd.Timedelta(days=simulation_days * 1.5)  # 考虑非交易日
               env_config["end_date"] = end_date.strftime("%Y-%m-%d")
   ```

2. **修改run_simulation_for_coalition方法**：

   ```python
   # 初始化交易环境
   coalition_id = self._generate_coalition_id(coalition_set)
   env = self._create_trading_environment(simulation_days, coalition_id, current_week_number)
   ```

## 测试验证

我们添加了以下测试来验证修复：

1. **测试不同周数的交易环境创建**：验证在不同周数下，交易环境的起始日期是否正确调整。

2. **测试run_simulation_for_coalition方法传递周数**：验证run_simulation_for_coalition方法是否正确传递current_week_number参数给_create_trading_environment方法。

测试结果表明，我们的修复是有效的，系统现在能够根据当前周数正确调整交易起始日期，避免重复执行相同的交易模拟。

## 结论

通过修改TradingSimulator的`_create_trading_environment`方法，使其能够根据当前周数调整交易起始日期，我们解决了系统在运行到第二周时仍然从第一周起始日期开始交易的问题。这些修改确保了系统能够正确处理不同周的交易，使周期性优化能够正常进行。