2025-07-24 22:16:57,506 - __main__ - INFO - ====================================================================================================
2025-07-24 22:16:57,507 - __main__ - INFO - 🚀 OPRO系统启动（重构版本）
2025-07-24 22:16:57,507 - __main__ - INFO - ====================================================================================================
2025-07-24 22:16:57,507 - __main__ - INFO - 运行模式: weekly
2025-07-24 22:16:57,507 - __main__ - INFO - LLM提供商: mock
2025-07-24 22:16:57,507 - __main__ - INFO - 系统架构: 重构版本（Service-Based Architecture）
2025-07-24 22:16:57,507 - __main__ - INFO - 🎭 使用虚拟LLM提供商 - 适用于快速测试，无需API密钥
2025-07-24 22:16:57,508 - __main__ - INFO - OPRO启用: True
2025-07-24 22:16:57,508 - __main__ - INFO - 📊 投资组合跟踪器: 启用（记录每日投资组合状态）
2025-07-24 22:16:57,508 - __main__ - INFO - 💾 持久化路径: 自动生成
2025-07-24 22:16:57,508 - __main__ - INFO - 🔄 跨周状态继承: 启用（支持周间状态传递）
2025-07-24 22:16:57,508 - __main__ - INFO - ✅ 完整模式: 跟踪器=True, 继承状态=True
2025-07-24 22:16:57,508 - __main__ - INFO - 周期性优化启用: 每 7 天
2025-07-24 22:16:57,511 - __main__ - INFO - 初始化重构版本系统...
2025-07-24 22:16:57,511 - __main__ - INFO - 🎭 虚拟LLM客户端初始化成功 (用于测试)
2025-07-24 22:16:57,511 - __main__ - INFO - 🔧 使用ServiceFactory创建重构版本的评估器...
2025-07-24 22:16:57,512 - contribution_assessment.infrastructure.configuration_manager - DEBUG - Updated config with 41 items
2025-07-24 22:16:57,512 - contribution_assessment.infrastructure.service_factory - DEBUG - Configured services with 41 configuration items
2025-07-24 22:16:57,512 - __main__ - INFO - 初始化RefactoredContributionAssessor组件...
2025-07-24 22:16:57,513 - contribution_assessment.infrastructure.configuration_manager - DEBUG - Updated config with 41 items
2025-07-24 22:16:57,513 - contribution_assessment.infrastructure.service_factory - DEBUG - Configured services with 41 configuration items
2025-07-24 22:16:57,517 - contribution_assessment.shapley_calculator.ShapleyCalculator - INFO - Shapley值计算器初始化完成
2025-07-24 22:16:57,522 - state_management.daily_state_manager - DEBUG - Daily states table initialized successfully
2025-07-24 22:16:57,522 - state_management.daily_state_manager - INFO - DailyStateManager initialized with storage: data/state_manager.db
2025-07-24 22:16:57,522 - __main__ - DEBUG - DailyStateManager初始化成功
2025-07-24 22:16:57,522 - __main__ - WARNING - ZhipuAI SDK (zhipuai) 未安装。请运行 'pip install zhipuai'
2025-07-24 22:16:57,523 - state_management.daily_state_manager - DEBUG - Daily states table initialized successfully
2025-07-24 22:16:57,523 - state_management.daily_state_manager - INFO - DailyStateManager initialized with storage: data/state_manager.db
2025-07-24 22:16:57,523 - contribution_assessment.infrastructure.event_bus - DEBUG - Added subscription sub_1 for event config_updated
2025-07-24 22:16:57,523 - contribution_assessment.infrastructure.event_bus - DEBUG - Added subscription sub_2 for event performance_alert
2025-07-24 22:16:57,523 - __main__ - DEBUG - 事件订阅完成
2025-07-24 22:16:57,523 - __main__ - INFO - 联盟服务初始化完成
2025-07-24 22:16:57,524 - __main__ - DEBUG - 已加载服务配置: {'start_date': '2025-01-01', 'end_date': '2025-02-15', 'stocks': ['AAPL'], 'starting_cash': 1000000, 'max_concurrent_simulations': 60}
2025-07-24 22:16:57,524 - contribution_assessment.infrastructure.event_bus - DEBUG - Added subscription sub_3 for event config_updated
2025-07-24 22:16:57,524 - contribution_assessment.infrastructure.event_bus - DEBUG - Added subscription sub_4 for event performance_alert
2025-07-24 22:16:57,524 - __main__ - DEBUG - 事件订阅完成
2025-07-24 22:16:57,524 - __main__ - INFO - 模拟服务初始化完成
2025-07-24 22:16:57,524 - __main__ - INFO - 简化OPRO服务初始化完成 (with historical data support)
2025-07-24 22:16:57,525 - contribution_assessment.services.state_manager - DEBUG - Database initialized successfully
2025-07-24 22:16:57,525 - contribution_assessment.services.state_manager - INFO - StateManager initialized with storage: data/state_manager.db
2025-07-24 22:16:57,525 - __main__ - INFO - ✅ RefactoredContributionAssessor初始化完成
2025-07-24 22:16:57,525 - __main__ - INFO - ✅ 重构版本的评估器创建成功
2025-07-24 22:16:57,525 - __main__ - INFO - ✅ 重构版本系统初始化完成
2025-07-24 22:16:57,525 - __main__ - INFO - 🔄 使用周期性优化模式
2025-07-24 22:16:57,526 - __main__ - INFO - 🔄 运行模式: 周期性在线优化（新架构WeeklyCycleManager）
2025-07-24 22:16:57,526 - __main__ - INFO - 📋 架构特点: 周 > 阶段 的层级结构
2025-07-24 22:16:57,526 - __main__ - INFO - 📋 周期性优化配置:
2025-07-24 22:16:57,526 - __main__ - INFO -    优化频率: 每 7 交易日
2025-07-24 22:16:57,526 - __main__ - INFO -    最少运行天数: 5 天
2025-07-24 22:16:57,526 - __main__ - INFO -    每周期最多优化: 1 个智能体
2025-07-24 22:16:57,526 - __main__ - INFO -    容错模式: 启用
2025-07-24 22:16:57,526 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-24 22:16:57,526 - __main__ - INFO - 🚀 使用WeeklyCycleManager架构（周 > 阶段）
2025-07-24 22:16:57,540 - __main__ - INFO - 📅 日期范围: 2025-01-01 到 2025-02-15
2025-07-24 22:16:57,541 - __main__ - INFO - 📊 交易日总数: 33 天
2025-07-24 22:16:57,541 - __main__ - INFO - 🔄 自动计算周数: 6 周 (每周5个交易日)
2025-07-24 22:16:57,544 - __main__ - INFO - 📊 启用日常投资组合跟踪器 (持久化: data/portfolio_tracker_20250724_221657.json)
2025-07-24 22:16:57,544 - __main__ - INFO - 🎭 虚拟LLM客户端初始化成功 (用于测试)
2025-07-24 22:16:57,544 - __main__ - INFO - ✅ 使用WeeklyOptimizationService作为优化执行器
2025-07-24 22:16:57,545 - __main__ - INFO - ✅ WeeklyCycleManager组件初始化成功
2025-07-24 22:16:57,545 - __main__ - INFO - ✅ WeeklyCycleManager创建成功，开始执行周期性优化
2025-07-24 22:16:57,545 - __main__ - INFO - 🚀 开始6周的优化循环
2025-07-24 22:16:57,545 - __main__ - INFO - 
📅 === 第1周优化循环 ===
2025-07-24 22:16:57,545 - __main__ - INFO - 🚀 开始第 1 周优化周期 (6阶段工作流)
2025-07-24 22:16:57,545 - __main__ - INFO - 创建智能体配置...
2025-07-24 22:16:57,545 - __main__ - INFO - 使用新架构创建智能体
2025-07-24 22:16:57,545 - contribution_assessment.infrastructure.configuration_manager - DEBUG - Updated config with 41 items
2025-07-24 22:16:57,546 - contribution_assessment.infrastructure.service_factory - DEBUG - Configured services with 41 configuration items
2025-07-24 22:16:57,546 - __main__ - INFO - 🎭 虚拟LLM客户端初始化成功 (用于测试)
2025-07-24 22:16:57,553 - __main__ - INFO - OPRO智能体 NAA 初始化完成 (OPRO: 启用)
2025-07-24 22:16:57,553 - __main__ - INFO - 创建智能体: NAA (OPRO: 启用)
2025-07-24 22:16:57,553 - __main__ - INFO - ✅ 智能体 NAA 创建成功
2025-07-24 22:16:57,553 - __main__ - INFO - OPRO智能体 TAA 初始化完成 (OPRO: 启用)
2025-07-24 22:16:57,553 - __main__ - INFO - 创建智能体: TAA (OPRO: 启用)
2025-07-24 22:16:57,553 - __main__ - INFO - ✅ 智能体 TAA 创建成功
2025-07-24 22:16:57,553 - __main__ - INFO - OPRO智能体 FAA 初始化完成 (OPRO: 启用)
2025-07-24 22:16:57,553 - __main__ - INFO - 创建智能体: FAA (OPRO: 启用)
2025-07-24 22:16:57,553 - __main__ - INFO - ✅ 智能体 FAA 创建成功
2025-07-24 22:16:57,553 - __main__ - INFO - OPRO智能体 BOA 初始化完成 (OPRO: 启用)
2025-07-24 22:16:57,553 - __main__ - INFO - 创建智能体: BOA (OPRO: 启用)
2025-07-24 22:16:57,554 - __main__ - INFO - ✅ 智能体 BOA 创建成功
2025-07-24 22:16:57,554 - __main__ - INFO - OPRO智能体 BeOA 初始化完成 (OPRO: 启用)
2025-07-24 22:16:57,554 - __main__ - INFO - 创建智能体: BeOA (OPRO: 启用)
2025-07-24 22:16:57,554 - __main__ - INFO - ✅ 智能体 BeOA 创建成功
2025-07-24 22:16:57,554 - __main__ - INFO - OPRO智能体 NOA 初始化完成 (OPRO: 启用)
2025-07-24 22:16:57,554 - __main__ - INFO - 创建智能体: NOA (OPRO: 启用)
2025-07-24 22:16:57,554 - __main__ - INFO - ✅ 智能体 NOA 创建成功
2025-07-24 22:16:57,554 - __main__ - INFO - OPRO智能体 TRA 初始化完成 (OPRO: 启用)
2025-07-24 22:16:57,554 - __main__ - INFO - 创建智能体: TRA (OPRO: 启用)
2025-07-24 22:16:57,554 - __main__ - INFO - ✅ 智能体 TRA 创建成功
2025-07-24 22:16:57,554 - __main__ - INFO - 智能体创建完成，成功: 7/7
2025-07-24 22:16:57,554 - __main__ - INFO - ✅ 新架构成功创建智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-24 22:16:57,554 - __main__ - INFO - 可用智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-24 22:16:57,554 - __main__ - INFO - 🔄 第1周使用旧版跨周状态传递
2025-07-24 22:16:57,555 - __main__ - INFO - 🔄 开始第 1 周评估周期 (6阶段工作流)
2025-07-24 22:16:57,555 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: weekly_workflow_started from phase_coordinator
2025-07-24 22:16:57,555 - __main__ - INFO - 📊 Phase 1: 联盟生成与子集运算
2025-07-24 22:16:57,555 - __main__ - INFO - Starting coalition_generation phase for workflow weekly_workflow_weekly_assessment_1_20250724_221657_week_1
2025-07-24 22:16:57,555 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: phase_started from phase_coordinator
2025-07-24 22:16:57,555 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: coalition_generation_started from CoalitionService
2025-07-24 22:16:57,555 - __main__ - INFO - 开始联盟生成: 目标智能体=7, 分析智能体=3, 交易智能体=TRA
2025-07-24 22:16:57,555 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 开始联盟生成和剪枝
2025-07-24 22:16:57,555 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 总智能体数: 7
2025-07-24 22:16:57,555 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 分析智能体: {'FAA', 'NAA', 'TAA'}
2025-07-24 22:16:57,555 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 交易智能体: TRA
2025-07-24 22:16:57,556 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 生成了 128 个初始联盟
2025-07-24 22:16:57,556 - __main__ - INFO - 限制联盟数量: 56 -> 50
2025-07-24 22:16:57,556 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: coalition_generation_completed from CoalitionService
2025-07-24 22:16:57,556 - __main__ - INFO - 联盟生成完成: 有效联盟=50, 剪枝联盟=72, 耗时=0.001s
2025-07-24 22:16:57,556 - __main__ - INFO - Coalition generation completed in 0.00s, generated 50 coalitions
2025-07-24 22:16:57,556 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: phase_completed from phase_coordinator
2025-07-24 22:16:57,556 - __main__ - INFO - 💹 Phase 2: 交易模拟
2025-07-24 22:16:57,556 - __main__ - INFO - Starting trading_simulation phase for workflow weekly_workflow_weekly_assessment_1_20250724_221657_week_1
2025-07-24 22:16:57,556 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: phase_started from phase_coordinator
2025-07-24 22:16:57,556 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: batch_simulation_started from SimulationService
2025-07-24 22:16:57,628 - __main__ - INFO - 开始批量模拟: 50 个联盟，最大并发数: 60
2025-07-24 22:16:57,878 - __main__ - INFO - 🚀 开始分阶段交易模拟...
2025-07-24 22:16:57,879 - __main__ - INFO - 📊 模拟计划:
2025-07-24 22:16:57,880 - __main__ - INFO -   - 完整联盟: {'FAA', 'BeOA', 'NAA', 'TAA', 'BOA', 'NOA', 'TRA'} (详细日志)
2025-07-24 22:16:57,880 - __main__ - INFO -   - 子集联盟: 49 个 (简洁日志)
2025-07-24 22:16:57,880 - __main__ - INFO - ======================================================================
2025-07-24 22:16:57,880 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-24 22:16:57,880 - __main__ - INFO - ======================================================================
2025-07-24 22:16:57,880 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-24 22:16:57,881 - contribution_assessment.infrastructure.event_bus - DEBUG - Publishing event: simulation_started from SimulationService
2025-07-24 22:16:57,881 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'NAA', 'TAA', 'BOA', 'NOA', 'TRA'}
2025-07-24 22:16:57,881 - __main__ - INFO - 📅 当前周数: 1
2025-07-24 22:16:57,881 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'NAA', 'TAA', 'BOA', 'NOA', 'TRA'})
2025-07-24 22:16:57,882 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=False
2025-07-24 22:16:58,061 - __main__ - INFO - 📅 设置当前模拟周数: 1 (来自参数)
2025-07-24 22:16:58,065 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-24 22:16:58,065 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-24 22:16:58,066 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 新闻22条
2025-07-24 22:16:58,198 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:16:58,199 - __main__ - DEBUG - NAA 记录IO数据: 上日收益率=0.000000, 记录总数=1
2025-07-24 22:16:58,200 - __main__ - INFO - 🤖 NAA 输出: 10 个字段
2025-07-24 22:16:58,200 - __main__ - INFO - 🤖 执行智能体 TAA...
2025-07-24 22:16:58,201 - __main__ - INFO - 🤖 TAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 价格30个点
2025-07-24 22:16:58,403 - __main__ - DEBUG - 🎭 返回 TAA 智能体的模拟响应
2025-07-24 22:16:58,404 - __main__ - DEBUG - TAA 记录IO数据: 上日收益率=0.000000, 记录总数=1
2025-07-24 22:16:58,404 - __main__ - INFO - 🤖 TAA 输出: 12 个字段
2025-07-24 22:16:58,404 - __main__ - INFO - 🤖 执行智能体 FAA...
2025-07-24 22:16:58,404 - __main__ - INFO - 🤖 FAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 基本面1个股票
2025-07-24 22:16:58,610 - __main__ - DEBUG - 🎭 返回 TAA 智能体的模拟响应
2025-07-24 22:16:58,610 - __main__ - DEBUG - FAA 记录IO数据: 上日收益率=0.000000, 记录总数=1
2025-07-24 22:16:58,610 - __main__ - INFO - 🤖 FAA 输出: 11 个字段
2025-07-24 22:16:58,610 - __main__ - INFO - ✅ 分析层完成: 3 个智能体
2025-07-24 22:16:58,610 - __main__ - INFO - 🤖 执行展望层智能体 BOA...
2025-07-24 22:16:58,610 - __main__ - INFO - 🤖 BOA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:16:58,906 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:16:58,906 - __main__ - DEBUG - BOA 记录IO数据: 上日收益率=0.000000, 记录总数=1
2025-07-24 22:16:58,906 - __main__ - INFO - 🤖 BOA 输出: confidence=0.6745728708668337
2025-07-24 22:16:58,907 - __main__ - INFO - ✅ 展望层智能体 BOA 完成: 信心度=0.6745728708668337, 推理=基于当前新闻分析，市场情绪相对稳定
2025-07-24 22:16:58,907 - __main__ - INFO - 🤖 执行展望层智能体 BeOA...
2025-07-24 22:16:58,907 - __main__ - INFO - 🤖 BeOA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:16:59,205 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:16:59,207 - __main__ - DEBUG - BeOA 记录IO数据: 上日收益率=0.000000, 记录总数=1
2025-07-24 22:16:59,207 - __main__ - INFO - 🤖 BeOA 输出: confidence=0.7206809938989067
2025-07-24 22:16:59,208 - __main__ - INFO - ✅ 展望层智能体 BeOA 完成: 信心度=0.7206809938989067, 推理=基于当前新闻分析，市场情绪相对稳定
2025-07-24 22:16:59,208 - __main__ - INFO - 🤖 执行展望层智能体 NOA...
2025-07-24 22:16:59,210 - __main__ - INFO - 🤖 NOA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:16:59,692 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:16:59,692 - __main__ - DEBUG - NOA 记录IO数据: 上日收益率=0.000000, 记录总数=1
2025-07-24 22:16:59,693 - __main__ - INFO - 🤖 NOA 输出: confidence=0.7330791290475267
2025-07-24 22:16:59,693 - __main__ - INFO - ✅ 展望层智能体 NOA 完成: 信心度=0.7330791290475267, 推理=基于当前新闻分析，市场情绪相对稳定
2025-07-24 22:16:59,693 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-24 22:16:59,694 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:16:59,999 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:00,000 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-24 22:17:00,019 - __main__ - INFO - 🔍 LLM响应前200字符: {'sentiment': 'neutral', 'confidence': 0.7231855084447333, 'key_factors': ['市场波动', '政策影响', '行业趋势'], 'recommendation': '持有', 'reasoning': '基于当前新闻分析，市场情绪相对稳定', 'timestamp': 1753366620.0001578, 'mock_res...
2025-07-24 22:17:00,023 - __main__ - INFO - 🔍 解析后结果: {'analysis': "{'sentiment': 'neutral', 'confidence': 0.7231855084447333, 'key_factors': ['市场波动', '政策影响', '行业趋势'], 'recommendation': '持有', 'reasoning': '基于当前新闻分析，市场情绪相对稳定', 'timestamp': 1753366620.0001578, 'mock_response': True}"}
2025-07-24 22:17:00,023 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-24 22:17:00,023 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-24 22:17:00,023 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-24 22:17:00,023 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-24 22:17:00,023 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-24 22:17:00,023 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': "{'sentiment': 'neutral', 'confidence': 0.7231855084447333, 'key_factors': ['市场波动', '政策影响', '行业趋势'], 'recommendation': '持有', 'reasoning': '基于当前新闻分析，市场情绪相对稳定', 'timestamp': 1753366620.0001578, 'mock_response': True}", 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-02'}
2025-07-24 22:17:00,023 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-24 22:17:00,036 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-24 22:17:00,037 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-24 22:17:00,037 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 新闻48条
2025-07-24 22:17:00,184 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:00,185 - __main__ - DEBUG - NAA 记录IO数据: 上日收益率=0.000000, 记录总数=2
2025-07-24 22:17:00,185 - __main__ - INFO - 🤖 NAA 输出: 10 个字段
2025-07-24 22:17:00,185 - __main__ - INFO - 🤖 执行智能体 TAA...
2025-07-24 22:17:00,185 - __main__ - INFO - 🤖 TAA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 价格30个点
2025-07-24 22:17:00,626 - __main__ - DEBUG - 🎭 返回 TAA 智能体的模拟响应
2025-07-24 22:17:00,626 - __main__ - DEBUG - TAA 记录IO数据: 上日收益率=0.000000, 记录总数=2
2025-07-24 22:17:00,626 - __main__ - INFO - 🤖 TAA 输出: 12 个字段
2025-07-24 22:17:00,626 - __main__ - INFO - 🤖 执行智能体 FAA...
2025-07-24 22:17:00,626 - __main__ - INFO - 🤖 FAA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 基本面1个股票
2025-07-24 22:17:00,846 - __main__ - DEBUG - 🎭 返回 TAA 智能体的模拟响应
2025-07-24 22:17:00,846 - __main__ - DEBUG - FAA 记录IO数据: 上日收益率=0.000000, 记录总数=2
2025-07-24 22:17:00,846 - __main__ - INFO - 🤖 FAA 输出: 11 个字段
2025-07-24 22:17:00,846 - __main__ - INFO - ✅ 分析层完成: 3 个智能体
2025-07-24 22:17:00,846 - __main__ - INFO - 🤖 执行展望层智能体 BOA...
2025-07-24 22:17:00,846 - __main__ - INFO - 🤖 BOA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:17:01,339 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:01,340 - __main__ - DEBUG - BOA 记录IO数据: 上日收益率=0.000000, 记录总数=2
2025-07-24 22:17:01,340 - __main__ - INFO - 🤖 BOA 输出: confidence=0.6084847307701476
2025-07-24 22:17:01,341 - __main__ - INFO - ✅ 展望层智能体 BOA 完成: 信心度=0.6084847307701476, 推理=基于当前新闻分析，市场情绪相对稳定
2025-07-24 22:17:01,341 - __main__ - INFO - 🤖 执行展望层智能体 BeOA...
2025-07-24 22:17:01,342 - __main__ - INFO - 🤖 BeOA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:17:01,754 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:01,754 - __main__ - DEBUG - BeOA 记录IO数据: 上日收益率=0.000000, 记录总数=2
2025-07-24 22:17:01,755 - __main__ - INFO - 🤖 BeOA 输出: confidence=0.7883054331340847
2025-07-24 22:17:01,755 - __main__ - INFO - ✅ 展望层智能体 BeOA 完成: 信心度=0.7883054331340847, 推理=基于当前新闻分析，市场情绪相对稳定
2025-07-24 22:17:01,755 - __main__ - INFO - 🤖 执行展望层智能体 NOA...
2025-07-24 22:17:01,755 - __main__ - INFO - 🤖 NOA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:17:02,233 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:02,234 - __main__ - DEBUG - NOA 记录IO数据: 上日收益率=0.000000, 记录总数=2
2025-07-24 22:17:02,234 - __main__ - INFO - 🤖 NOA 输出: confidence=0.656292150747571
2025-07-24 22:17:02,234 - __main__ - INFO - ✅ 展望层智能体 NOA 完成: 信心度=0.656292150747571, 推理=基于当前新闻分析，市场情绪相对稳定
2025-07-24 22:17:02,234 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-24 22:17:02,234 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:17:02,633 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:02,633 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-24 22:17:02,633 - __main__ - INFO - 🔍 LLM响应前200字符: {'sentiment': 'neutral', 'confidence': 0.7795343477797233, 'key_factors': ['市场波动', '政策影响', '行业趋势'], 'recommendation': '持有', 'reasoning': '基于当前新闻分析，市场情绪相对稳定', 'timestamp': 1753366622.6335518, 'mock_res...
2025-07-24 22:17:02,634 - __main__ - INFO - 🔍 解析后结果: {'analysis': "{'sentiment': 'neutral', 'confidence': 0.7795343477797233, 'key_factors': ['市场波动', '政策影响', '行业趋势'], 'recommendation': '持有', 'reasoning': '基于当前新闻分析，市场情绪相对稳定', 'timestamp': 1753366622.6335518, 'mock_response': True}"}
2025-07-24 22:17:02,634 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-24 22:17:02,634 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-24 22:17:02,635 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-24 22:17:02,635 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-24 22:17:02,635 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-24 22:17:02,635 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': "{'sentiment': 'neutral', 'confidence': 0.7795343477797233, 'key_factors': ['市场波动', '政策影响', '行业趋势'], 'recommendation': '持有', 'reasoning': '基于当前新闻分析，市场情绪相对稳定', 'timestamp': 1753366622.6335518, 'mock_response': True}", 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-03'}
2025-07-24 22:17:02,635 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-24 22:17:02,655 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-24 22:17:02,655 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-24 22:17:02,655 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 新闻32条
2025-07-24 22:17:02,818 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:02,819 - __main__ - DEBUG - NAA 记录IO数据: 上日收益率=0.000000, 记录总数=3
2025-07-24 22:17:02,819 - __main__ - INFO - 🤖 NAA 输出: 10 个字段
2025-07-24 22:17:02,819 - __main__ - INFO - 🤖 执行智能体 TAA...
2025-07-24 22:17:02,819 - __main__ - INFO - 🤖 TAA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 价格30个点
2025-07-24 22:17:03,029 - __main__ - DEBUG - 🎭 返回 TAA 智能体的模拟响应
2025-07-24 22:17:03,029 - __main__ - DEBUG - TAA 记录IO数据: 上日收益率=0.000000, 记录总数=3
2025-07-24 22:17:03,029 - __main__ - INFO - 🤖 TAA 输出: 12 个字段
2025-07-24 22:17:03,030 - __main__ - INFO - 🤖 执行智能体 FAA...
2025-07-24 22:17:03,030 - __main__ - INFO - 🤖 FAA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 基本面1个股票
2025-07-24 22:17:03,242 - __main__ - DEBUG - 🎭 返回 TAA 智能体的模拟响应
2025-07-24 22:17:03,242 - __main__ - DEBUG - FAA 记录IO数据: 上日收益率=0.000000, 记录总数=3
2025-07-24 22:17:03,243 - __main__ - INFO - 🤖 FAA 输出: 11 个字段
2025-07-24 22:17:03,243 - __main__ - INFO - ✅ 分析层完成: 3 个智能体
2025-07-24 22:17:03,244 - __main__ - INFO - 🤖 执行展望层智能体 BOA...
2025-07-24 22:17:03,244 - __main__ - INFO - 🤖 BOA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:17:03,613 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:03,613 - __main__ - DEBUG - BOA 记录IO数据: 上日收益率=0.000000, 记录总数=3
2025-07-24 22:17:03,613 - __main__ - INFO - 🤖 BOA 输出: confidence=0.6950190884797648
2025-07-24 22:17:03,613 - __main__ - INFO - ✅ 展望层智能体 BOA 完成: 信心度=0.6950190884797648, 推理=基于当前新闻分析，市场情绪相对稳定
2025-07-24 22:17:03,614 - __main__ - INFO - 🤖 执行展望层智能体 BeOA...
2025-07-24 22:17:03,614 - __main__ - INFO - 🤖 BeOA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:17:03,841 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:03,841 - __main__ - DEBUG - BeOA 记录IO数据: 上日收益率=0.000000, 记录总数=3
2025-07-24 22:17:03,841 - __main__ - INFO - 🤖 BeOA 输出: confidence=0.6560783120251464
2025-07-24 22:17:03,842 - __main__ - INFO - ✅ 展望层智能体 BeOA 完成: 信心度=0.6560783120251464, 推理=基于当前新闻分析，市场情绪相对稳定
2025-07-24 22:17:03,842 - __main__ - INFO - 🤖 执行展望层智能体 NOA...
2025-07-24 22:17:03,842 - __main__ - INFO - 🤖 NOA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:17:04,176 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:04,176 - __main__ - DEBUG - NOA 记录IO数据: 上日收益率=0.000000, 记录总数=3
2025-07-24 22:17:04,177 - __main__ - INFO - 🤖 NOA 输出: confidence=0.7304165330954507
2025-07-24 22:17:04,177 - __main__ - INFO - ✅ 展望层智能体 NOA 完成: 信心度=0.7304165330954507, 推理=基于当前新闻分析，市场情绪相对稳定
2025-07-24 22:17:04,177 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-24 22:17:04,178 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:17:04,492 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:04,492 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-24 22:17:04,492 - __main__ - INFO - 🔍 LLM响应前200字符: {'sentiment': 'neutral', 'confidence': 0.7252449449650685, 'key_factors': ['市场波动', '政策影响', '行业趋势'], 'recommendation': '持有', 'reasoning': '基于当前新闻分析，市场情绪相对稳定', 'timestamp': 1753366624.4924319, 'mock_res...
2025-07-24 22:17:04,492 - __main__ - INFO - 🔍 解析后结果: {'analysis': "{'sentiment': 'neutral', 'confidence': 0.7252449449650685, 'key_factors': ['市场波动', '政策影响', '行业趋势'], 'recommendation': '持有', 'reasoning': '基于当前新闻分析，市场情绪相对稳定', 'timestamp': 1753366624.4924319, 'mock_response': True}"}
2025-07-24 22:17:04,492 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-24 22:17:04,492 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-24 22:17:04,492 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-24 22:17:04,492 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-24 22:17:04,493 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-24 22:17:04,493 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': "{'sentiment': 'neutral', 'confidence': 0.7252449449650685, 'key_factors': ['市场波动', '政策影响', '行业趋势'], 'recommendation': '持有', 'reasoning': '基于当前新闻分析，市场情绪相对稳定', 'timestamp': 1753366624.4924319, 'mock_response': True}", 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-06'}
2025-07-24 22:17:04,493 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-24 22:17:04,505 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-24 22:17:04,506 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-24 22:17:04,506 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-07, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 新闻46条
2025-07-24 22:17:04,840 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:04,841 - __main__ - DEBUG - NAA 记录IO数据: 上日收益率=0.000000, 记录总数=4
2025-07-24 22:17:04,841 - __main__ - INFO - 🤖 NAA 输出: 10 个字段
2025-07-24 22:17:04,841 - __main__ - INFO - 🤖 执行智能体 TAA...
2025-07-24 22:17:04,841 - __main__ - INFO - 🤖 TAA 输入: 日期=2025-01-07, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 价格30个点
2025-07-24 22:17:05,058 - __main__ - DEBUG - 🎭 返回 TAA 智能体的模拟响应
2025-07-24 22:17:05,059 - __main__ - DEBUG - TAA 记录IO数据: 上日收益率=0.000000, 记录总数=4
2025-07-24 22:17:05,059 - __main__ - INFO - 🤖 TAA 输出: 12 个字段
2025-07-24 22:17:05,059 - __main__ - INFO - 🤖 执行智能体 FAA...
2025-07-24 22:17:05,059 - __main__ - INFO - 🤖 FAA 输入: 日期=2025-01-07, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 基本面1个股票
2025-07-24 22:17:05,456 - __main__ - DEBUG - 🎭 返回 TAA 智能体的模拟响应
2025-07-24 22:17:05,457 - __main__ - DEBUG - FAA 记录IO数据: 上日收益率=0.000000, 记录总数=4
2025-07-24 22:17:05,457 - __main__ - INFO - 🤖 FAA 输出: 11 个字段
2025-07-24 22:17:05,458 - __main__ - INFO - ✅ 分析层完成: 3 个智能体
2025-07-24 22:17:05,458 - __main__ - INFO - 🤖 执行展望层智能体 BOA...
2025-07-24 22:17:05,458 - __main__ - INFO - 🤖 BOA 输入: 日期=2025-01-07, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:17:05,759 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:05,759 - __main__ - DEBUG - BOA 记录IO数据: 上日收益率=0.000000, 记录总数=4
2025-07-24 22:17:05,759 - __main__ - INFO - 🤖 BOA 输出: confidence=0.6826634727957962
2025-07-24 22:17:05,759 - __main__ - INFO - ✅ 展望层智能体 BOA 完成: 信心度=0.6826634727957962, 推理=基于当前新闻分析，市场情绪相对稳定
2025-07-24 22:17:05,759 - __main__ - INFO - 🤖 执行展望层智能体 BeOA...
2025-07-24 22:17:05,759 - __main__ - INFO - 🤖 BeOA 输入: 日期=2025-01-07, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:17:06,254 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:06,255 - __main__ - DEBUG - BeOA 记录IO数据: 上日收益率=0.000000, 记录总数=4
2025-07-24 22:17:06,255 - __main__ - INFO - 🤖 BeOA 输出: confidence=0.784274792150053
2025-07-24 22:17:06,255 - __main__ - INFO - ✅ 展望层智能体 BeOA 完成: 信心度=0.784274792150053, 推理=基于当前新闻分析，市场情绪相对稳定
2025-07-24 22:17:06,256 - __main__ - INFO - 🤖 执行展望层智能体 NOA...
2025-07-24 22:17:06,256 - __main__ - INFO - 🤖 NOA 输入: 日期=2025-01-07, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:17:06,643 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:06,643 - __main__ - DEBUG - NOA 记录IO数据: 上日收益率=0.000000, 记录总数=4
2025-07-24 22:17:06,644 - __main__ - INFO - 🤖 NOA 输出: confidence=0.6881380598344164
2025-07-24 22:17:06,644 - __main__ - INFO - ✅ 展望层智能体 NOA 完成: 信心度=0.6881380598344164, 推理=基于当前新闻分析，市场情绪相对稳定
2025-07-24 22:17:06,644 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-24 22:17:06,644 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-07, 累计收益=0.0000, 周收益=0.0000, 持仓1个
2025-07-24 22:17:06,937 - __main__ - DEBUG - 🎭 返回 NAA 智能体的模拟响应
2025-07-24 22:17:06,938 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-24 22:17:06,939 - __main__ - INFO - 🔍 LLM响应前200字符: {'sentiment': 'neutral', 'confidence': 0.7539529600540321, 'key_factors': ['市场波动', '政策影响', '行业趋势'], 'recommendation': '持有', 'reasoning': '基于当前新闻分析，市场情绪相对稳定', 'timestamp': 1753366626.938252, 'mock_resp...
2025-07-24 22:17:06,940 - __main__ - INFO - 🔍 解析后结果: {'analysis': "{'sentiment': 'neutral', 'confidence': 0.7539529600540321, 'key_factors': ['市场波动', '政策影响', '行业趋势'], 'recommendation': '持有', 'reasoning': '基于当前新闻分析，市场情绪相对稳定', 'timestamp': 1753366626.938252, 'mock_response': True}"}
2025-07-24 22:17:06,940 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-24 22:17:06,940 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-24 22:17:06,940 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-24 22:17:06,941 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-24 22:17:06,942 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-24 22:17:06,942 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': "{'sentiment': 'neutral', 'confidence': 0.7539529600540321, 'key_factors': ['市场波动', '政策影响', '行业趋势'], 'recommendation': '持有', 'reasoning': '基于当前新闻分析，市场情绪相对稳定', 'timestamp': 1753366626.938252, 'mock_response': True}", 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-07'}
2025-07-24 22:17:06,942 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-24 22:17:06,959 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-24 22:17:06,959 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-24 22:17:06,959 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-08, 累计收益=0.0000, 周收益=0.0000, 持仓1个, 数据: 新闻16条
