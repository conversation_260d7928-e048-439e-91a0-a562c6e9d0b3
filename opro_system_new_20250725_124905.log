2025-07-25 12:49:05,274 - __main__ - INFO - ====================================================================================================
2025-07-25 12:49:05,275 - __main__ - INFO - 🚀 OPRO系统启动（重构版本）
2025-07-25 12:49:05,275 - __main__ - INFO - ====================================================================================================
2025-07-25 12:49:05,275 - __main__ - INFO - 运行模式: weekly
2025-07-25 12:49:05,275 - __main__ - INFO - LLM提供商: zhipuai
2025-07-25 12:49:05,275 - __main__ - INFO - 系统架构: 重构版本（Service-Based Architecture）
2025-07-25 12:49:05,275 - __main__ - INFO - OPRO启用: True
2025-07-25 12:49:05,275 - __main__ - INFO - 📊 投资组合跟踪器: 启用（记录每日投资组合状态）
2025-07-25 12:49:05,275 - __main__ - INFO - 💾 持久化路径: 自动生成
2025-07-25 12:49:05,275 - __main__ - INFO - 🔄 跨周状态继承: 启用（支持周间状态传递）
2025-07-25 12:49:05,275 - __main__ - INFO - ✅ 完整模式: 跟踪器=True, 继承状态=True
2025-07-25 12:49:05,275 - __main__ - INFO - 周期性优化启用: 每 7 天
2025-07-25 12:49:05,278 - __main__ - INFO - 初始化重构版本系统...
2025-07-25 12:49:05,382 - __main__ - INFO - 🔧 使用ServiceFactory创建重构版本的评估器...
2025-07-25 12:49:05,382 - __main__ - INFO - 初始化RefactoredContributionAssessor组件...
2025-07-25 12:49:05,384 - contribution_assessment.shapley_calculator.ShapleyCalculator - INFO - Shapley值计算器初始化完成
2025-07-25 12:49:05,388 - state_management.daily_state_manager - INFO - DailyStateManager initialized with storage: data/state_manager.db
2025-07-25 12:49:05,404 - state_management.daily_state_manager - INFO - DailyStateManager initialized with storage: data/state_manager.db
2025-07-25 12:49:05,405 - __main__ - INFO - 联盟服务初始化完成
2025-07-25 12:49:05,405 - __main__ - INFO - 模拟服务初始化完成
2025-07-25 12:49:05,405 - __main__ - INFO - 简化OPRO服务初始化完成 (with historical data support)
2025-07-25 12:49:05,405 - contribution_assessment.services.state_manager - INFO - StateManager initialized with storage: data/state_manager.db
2025-07-25 12:49:05,405 - __main__ - INFO - ✅ RefactoredContributionAssessor初始化完成
2025-07-25 12:49:05,405 - __main__ - INFO - ✅ 重构版本的评估器创建成功
2025-07-25 12:49:05,405 - __main__ - INFO - ✅ 重构版本系统初始化完成
2025-07-25 12:49:05,405 - __main__ - INFO - 🔄 使用周期性优化模式
2025-07-25 12:49:05,405 - __main__ - INFO - 🔄 运行模式: 周期性在线优化（新架构WeeklyCycleManager）
2025-07-25 12:49:05,405 - __main__ - INFO - 📋 架构特点: 周 > 阶段 的层级结构
2025-07-25 12:49:05,405 - __main__ - INFO - 📋 周期性优化配置:
2025-07-25 12:49:05,405 - __main__ - INFO -    优化频率: 每 7 交易日
2025-07-25 12:49:05,405 - __main__ - INFO -    最少运行天数: 5 天
2025-07-25 12:49:05,405 - __main__ - INFO -    每周期最多优化: 1 个智能体
2025-07-25 12:49:05,405 - __main__ - INFO -    容错模式: 启用
2025-07-25 12:49:05,405 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-25 12:49:05,405 - __main__ - INFO - 🚀 使用WeeklyCycleManager架构（周 > 阶段）
2025-07-25 12:49:05,411 - __main__ - INFO - 📅 日期范围: 2025-01-01 到 2025-02-15
2025-07-25 12:49:05,411 - __main__ - INFO - 📊 交易日总数: 33 天
2025-07-25 12:49:05,411 - __main__ - INFO - 🔄 自动计算周数: 6 周 (每周5个交易日)
2025-07-25 12:49:05,415 - __main__ - INFO - 📊 启用日常投资组合跟踪器 (持久化: data/portfolio_tracker_20250725_124905.json)
2025-07-25 12:49:05,433 - __main__ - INFO - ✅ 使用WeeklyOptimizationService作为优化执行器
2025-07-25 12:49:05,433 - __main__ - INFO - ✅ WeeklyCycleManager组件初始化成功
2025-07-25 12:49:05,433 - __main__ - INFO - ✅ WeeklyCycleManager创建成功，开始执行周期性优化
2025-07-25 12:49:05,433 - __main__ - INFO - 🚀 开始6周的优化循环
2025-07-25 12:49:05,433 - __main__ - INFO - 
📅 === 第1周优化循环 ===
2025-07-25 12:49:05,433 - __main__ - INFO - 🚀 开始第 1 周优化周期 (6阶段工作流)
2025-07-25 12:49:05,433 - __main__ - INFO - 创建智能体配置...
2025-07-25 12:49:05,433 - __main__ - INFO - 使用新架构创建智能体
2025-07-25 12:49:05,455 - __main__ - INFO - OPRO智能体 NAA 初始化完成 (OPRO: 启用)
2025-07-25 12:49:05,455 - __main__ - INFO - 创建智能体: NAA (OPRO: 启用)
2025-07-25 12:49:05,455 - __main__ - INFO - ✅ 智能体 NAA 创建成功
2025-07-25 12:49:05,455 - __main__ - INFO - OPRO智能体 TAA 初始化完成 (OPRO: 启用)
2025-07-25 12:49:05,455 - __main__ - INFO - 创建智能体: TAA (OPRO: 启用)
2025-07-25 12:49:05,455 - __main__ - INFO - ✅ 智能体 TAA 创建成功
2025-07-25 12:49:05,455 - __main__ - INFO - OPRO智能体 FAA 初始化完成 (OPRO: 启用)
2025-07-25 12:49:05,455 - __main__ - INFO - 创建智能体: FAA (OPRO: 启用)
2025-07-25 12:49:05,455 - __main__ - INFO - ✅ 智能体 FAA 创建成功
2025-07-25 12:49:05,455 - __main__ - INFO - OPRO智能体 BOA 初始化完成 (OPRO: 启用)
2025-07-25 12:49:05,455 - __main__ - INFO - 创建智能体: BOA (OPRO: 启用)
2025-07-25 12:49:05,455 - __main__ - INFO - ✅ 智能体 BOA 创建成功
2025-07-25 12:49:05,455 - __main__ - INFO - OPRO智能体 BeOA 初始化完成 (OPRO: 启用)
2025-07-25 12:49:05,455 - __main__ - INFO - 创建智能体: BeOA (OPRO: 启用)
2025-07-25 12:49:05,455 - __main__ - INFO - ✅ 智能体 BeOA 创建成功
2025-07-25 12:49:05,455 - __main__ - INFO - OPRO智能体 NOA 初始化完成 (OPRO: 启用)
2025-07-25 12:49:05,455 - __main__ - INFO - 创建智能体: NOA (OPRO: 启用)
2025-07-25 12:49:05,455 - __main__ - INFO - ✅ 智能体 NOA 创建成功
2025-07-25 12:49:05,455 - __main__ - INFO - OPRO智能体 TRA 初始化完成 (OPRO: 启用)
2025-07-25 12:49:05,455 - __main__ - INFO - 创建智能体: TRA (OPRO: 启用)
2025-07-25 12:49:05,456 - __main__ - INFO - ✅ 智能体 TRA 创建成功
2025-07-25 12:49:05,456 - __main__ - INFO - 智能体创建完成，成功: 7/7
2025-07-25 12:49:05,456 - __main__ - INFO - ✅ 新架构成功创建智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-25 12:49:05,456 - __main__ - INFO - 可用智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-25 12:49:05,456 - __main__ - INFO - 🔄 第1周使用旧版跨周状态传递
2025-07-25 12:49:05,456 - __main__ - INFO - 🔄 开始第 1 周评估周期 (6阶段工作流)
2025-07-25 12:49:05,456 - __main__ - INFO - 📊 Phase 1: 联盟生成与子集运算
2025-07-25 12:49:05,456 - __main__ - INFO - Starting coalition_generation phase for workflow weekly_workflow_weekly_assessment_1_20250725_124905_week_1
2025-07-25 12:49:05,456 - __main__ - INFO - 开始联盟生成: 目标智能体=7, 分析智能体=3, 交易智能体=TRA
2025-07-25 12:49:05,456 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 开始联盟生成和剪枝
2025-07-25 12:49:05,456 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 总智能体数: 7
2025-07-25 12:49:05,456 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 分析智能体: {'NAA', 'FAA', 'TAA'}
2025-07-25 12:49:05,456 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 交易智能体: TRA
2025-07-25 12:49:05,456 - contribution_assessment.coalition_manager.CoalitionManager - INFO - 生成了 128 个初始联盟
2025-07-25 12:49:05,456 - __main__ - INFO - 限制联盟数量: 56 -> 50
2025-07-25 12:49:05,456 - __main__ - INFO - 联盟生成完成: 有效联盟=50, 剪枝联盟=72, 耗时=0.000s
2025-07-25 12:49:05,456 - __main__ - INFO - Coalition generation completed in 0.00s, generated 50 coalitions
2025-07-25 12:49:05,456 - __main__ - INFO - 💹 Phase 2: 交易模拟
2025-07-25 12:49:05,456 - __main__ - INFO - Starting trading_simulation phase for workflow weekly_workflow_weekly_assessment_1_20250725_124905_week_1
2025-07-25 12:49:05,456 - __main__ - INFO - 开始批量模拟: 50 个联盟，最大并发数: 60
2025-07-25 12:49:05,456 - __main__ - INFO - 🚀 开始分阶段交易模拟...
2025-07-25 12:49:05,456 - __main__ - INFO - 📊 模拟计划:
2025-07-25 12:49:05,456 - __main__ - INFO -   - 完整联盟: {'NOA', 'NAA', 'FAA', 'BOA', 'TRA', 'BeOA', 'TAA'} (详细日志)
2025-07-25 12:49:05,456 - __main__ - INFO -   - 子集联盟: 49 个 (简洁日志)
2025-07-25 12:49:05,456 - __main__ - INFO - ======================================================================
2025-07-25 12:49:05,456 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-25 12:49:05,456 - __main__ - INFO - ======================================================================
2025-07-25 12:49:05,456 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-25 12:49:05,457 - __main__ - INFO - 开始模拟联盟: {'NOA', 'NAA', 'FAA', 'BOA', 'TRA', 'BeOA', 'TAA'}
2025-07-25 12:49:05,457 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 12:49:05,457 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'NAA', 'FAA', 'BOA', 'TRA', 'BeOA', 'TAA'})
2025-07-25 12:49:05,457 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=False
2025-07-25 12:49:05,555 - __main__ - INFO - 📅 设置当前模拟周数: 1 (来自参数)
2025-07-25 12:49:05,556 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-25 12:49:05,556 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-25 12:49:05,556 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 数据: 新闻22条
2025-07-25 12:49:58,360 - __main__ - INFO - 🤖 NAA 输出: 5 个字段
2025-07-25 12:49:58,361 - __main__ - INFO - 🤖 执行智能体 TAA...
2025-07-25 12:49:58,361 - __main__ - INFO - 🤖 TAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 数据: 价格30个点
2025-07-25 12:51:00,413 - __main__ - INFO - 🤖 TAA 输出: 6 个字段
2025-07-25 12:51:00,415 - __main__ - INFO - 🤖 执行智能体 FAA...
2025-07-25 12:51:00,415 - __main__ - INFO - 🤖 FAA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000, 数据: 基本面1个股票
2025-07-25 12:51:32,082 - __main__ - INFO - 🤖 FAA 输出: 13 个字段
2025-07-25 12:51:32,083 - __main__ - INFO - ✅ 分析层完成: 3 个智能体
2025-07-25 12:51:32,084 - __main__ - INFO - 🤖 执行展望层智能体 BOA...
2025-07-25 12:51:32,084 - __main__ - INFO - 🤖 BOA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000
2025-07-25 12:52:14,198 - __main__ - INFO - 🤖 BOA 输出: 5 个字段
2025-07-25 12:52:14,198 - __main__ - INFO - ✅ 展望层智能体 BOA 完成: 已输出结果
2025-07-25 12:52:14,199 - __main__ - INFO - 🤖 执行展望层智能体 BeOA...
2025-07-25 12:52:14,199 - __main__ - INFO - 🤖 BeOA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000
2025-07-25 12:52:51,339 - __main__ - INFO - 🤖 BeOA 输出: 5 个字段
2025-07-25 12:52:51,340 - __main__ - INFO - ✅ 展望层智能体 BeOA 完成: 已输出结果
2025-07-25 12:52:51,340 - __main__ - INFO - 🤖 执行展望层智能体 NOA...
2025-07-25 12:52:51,340 - __main__ - INFO - 🤖 NOA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000
2025-07-25 12:53:31,634 - __main__ - INFO - 🤖 NOA 输出: 5 个字段
2025-07-25 12:53:31,635 - __main__ - INFO - ✅ 展望层智能体 NOA 完成: 已输出结果
2025-07-25 12:53:31,635 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-25 12:53:31,635 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000
2025-07-25 12:53:31,635 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 3196
2025-07-25 12:53:31,635 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 12:53:31,635 - __main__ - INFO - 🔍 提示词结尾200字符: ...（看跌）\n- 预计短期内，特别是在2025年1月2日之后，科技股，尤其是苹果公司（AAPL）的... (信心度: 未知)
  • NOA: {'analysis': '📅 分析日期: 2025-01-02\n\n🤖 分析层智能体输出分析：\n\n- **市场展望（中性）**：市场情绪评分0.6，显示出一定的乐观情绪，但并非全面看涨。科技巨... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 12:54:00,422 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-25 12:54:00,423 - __main__ - INFO - 🔍 LLM响应前200字符: {'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示市场情绪较为积极，苹果公司的CEO Tim Cook等科技巨头发布了积极的展望，同时苹果在中国市场对iPhone 16系列进行了降价。然而，瑞士信贷银行下调了苹果的iPhone销量预测，这可能会对苹果的股价产生负面影响。TAA显示短期趋势可能为看跌，技术指标如RSI和MACD可能显示市场处于超卖状态。...
2025-07-25 12:54:00,423 - __main__ - INFO - 🔍 解析后结果: {'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示市场情绪较为积极，苹果公司的CEO Tim Cook等科技巨头发布了积极的展望，同时苹果在中国市场对iPhone 16系列进行了降价。然而，瑞士信贷银行下调了苹果的iPhone销量预测，这可能会对苹果的股价产生负面影响。TAA显示短期趋势可能为看跌，技术指标如RSI和MACD可能显示市场处于超卖状态。FAA显示苹果的估值合理，财务健康得分较高，竞争地位领先。展望层智能体中，BOA和BeOA给出了相反的信号，BOA认为市场情绪积极，而BeOA则预计短期内科技股将面临压力。NOA认为市场应保持中性观望。综合分析，尽管市场情绪积极，但技术分析和基本面分析显示了一些潜在的下行风险。因此，我们倾向于采取中性观望的策略。**最终决策：持有**。'}
2025-07-25 12:54:00,424 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-25 12:54:00,424 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-25 12:54:00,424 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-25 12:54:00,424 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-25 12:54:00,424 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-25 12:54:00,424 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示市场情绪较为积极，苹果公司的CEO Tim Cook等科技巨头发布了积极的展望，同时苹果在中国市场对iPhone 16系列进行了降价。然而，瑞士信贷银行下调了苹果的iPhone销量预测，这可能会对苹果的股价产生负面影响。TAA显示短期趋势可能为看跌，技术指标如RSI和MACD可能显示市场处于超卖状态。FAA显示苹果的估值合理，财务健康得分较高，竞争地位领先。展望层智能体中，BOA和BeOA给出了相反的信号，BOA认为市场情绪积极，而BeOA则预计短期内科技股将面临压力。NOA认为市场应保持中性观望。综合分析，尽管市场情绪积极，但技术分析和基本面分析显示了一些潜在的下行风险。因此，我们倾向于采取中性观望的策略。**最终决策：持有**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-02'}
2025-07-25 12:54:00,424 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-25 12:54:00,430 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-25 12:54:00,430 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-25 12:54:00,430 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 数据: 新闻48条
2025-07-25 12:54:49,067 - __main__ - INFO - 🤖 NAA 输出: 5 个字段
2025-07-25 12:54:49,068 - __main__ - INFO - 🤖 执行智能体 TAA...
2025-07-25 12:54:49,068 - __main__ - INFO - 🤖 TAA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 数据: 价格30个点
2025-07-25 12:55:32,041 - __main__ - INFO - 🤖 TAA 输出: 6 个字段
2025-07-25 12:55:32,042 - __main__ - INFO - 🤖 执行智能体 FAA...
2025-07-25 12:55:32,042 - __main__ - INFO - 🤖 FAA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000, 数据: 基本面1个股票
2025-07-25 12:56:04,717 - __main__ - INFO - 🤖 FAA 输出: 13 个字段
2025-07-25 12:56:04,718 - __main__ - INFO - ✅ 分析层完成: 3 个智能体
2025-07-25 12:56:04,718 - __main__ - INFO - 🤖 执行展望层智能体 BOA...
2025-07-25 12:56:04,718 - __main__ - INFO - 🤖 BOA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000
2025-07-25 12:56:33,024 - __main__ - INFO - 🤖 BOA 输出: 5 个字段
2025-07-25 12:56:33,025 - __main__ - INFO - ✅ 展望层智能体 BOA 完成: 已输出结果
2025-07-25 12:56:33,025 - __main__ - INFO - 🤖 执行展望层智能体 BeOA...
2025-07-25 12:56:33,025 - __main__ - INFO - 🤖 BeOA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000
2025-07-25 12:57:29,013 - __main__ - INFO - 🤖 BeOA 输出: 5 个字段
2025-07-25 12:57:29,014 - __main__ - INFO - ✅ 展望层智能体 BeOA 完成: 已输出结果
2025-07-25 12:57:29,014 - __main__ - INFO - 🤖 执行展望层智能体 NOA...
2025-07-25 12:57:29,014 - __main__ - INFO - 🤖 NOA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000
2025-07-25 12:58:25,154 - __main__ - INFO - 🤖 NOA 输出: 5 个字段
2025-07-25 12:58:25,154 - __main__ - INFO - ✅ 展望层智能体 NOA 完成: 已输出结果
2025-07-25 12:58:25,154 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-25 12:58:25,154 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000
2025-07-25 12:58:25,155 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 3870
2025-07-25 12:58:25,155 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 12:58:25,155 - __main__ - INFO - 🔍 提示词结尾200字符: ...分析层智能体的输出，以下是对市场的看跌展望：\n\n- **看跌因素列表**:\n  1. **... (信心度: 未知)
  • NOA: {'analysis': '📅 分析日期: 2025-01-03\n\n### 市场展望（中性）\n当前市场展现出中性趋势，没有明显的看涨或看跌信号。情绪评分0.6表明市场情绪较为稳定，但存在一定的不... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 12:58:47,968 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-25 12:58:47,968 - __main__ - INFO - 🔍 LLM响应前200字符: {'analysis': '在2025-01-03的分析中，展望层智能体的输出显示了一个复杂的市场情况。BOA给出了看涨信号，主要基于网中立性规则的推翻、苹果公司就Siri隐私违规问题达成和解以及中国扩大消费补贴范围等因素。这些因素对苹果等科技公司有利，可能导致市场情绪偏向看涨。然而，BeOA给出了看跌信号，主要基于苹果iPhone在中国市场份额下降等因素。这些因素可能对苹果公司的业绩产生负面影响...
2025-07-25 12:58:47,968 - __main__ - INFO - 🔍 解析后结果: {'analysis': '在2025-01-03的分析中，展望层智能体的输出显示了一个复杂的市场情况。BOA给出了看涨信号，主要基于网中立性规则的推翻、苹果公司就Siri隐私违规问题达成和解以及中国扩大消费补贴范围等因素。这些因素对苹果等科技公司有利，可能导致市场情绪偏向看涨。然而，BeOA给出了看跌信号，主要基于苹果iPhone在中国市场份额下降等因素。这些因素可能对苹果公司的业绩产生负面影响，导致市场情绪偏向看跌。NOA认为市场当前处于中性趋势，没有明显的看涨或看跌信号。分析层智能体的NAA、TAA和FAA结果也显示了一个中性或复杂的市场情况。NAA的情绪评分和新闻摘要显示了一些正面和负面的新闻，TAA的技术分析显示市场处于中性状态，而FAA的基本面分析显示苹果公司的估值合理，财务状况良好，竞争地位领先。综合以上分析，虽然BOA和BeOA给出了相反的信号，但NOA的中性展望提供了平衡的观点。因此，最终决策是持有，以避免在市场不确定性中采取过于激进的操作。'}
2025-07-25 12:58:47,968 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-25 12:58:47,968 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-25 12:58:47,968 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-25 12:58:47,968 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-25 12:58:47,968 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-25 12:58:47,968 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在2025-01-03的分析中，展望层智能体的输出显示了一个复杂的市场情况。BOA给出了看涨信号，主要基于网中立性规则的推翻、苹果公司就Siri隐私违规问题达成和解以及中国扩大消费补贴范围等因素。这些因素对苹果等科技公司有利，可能导致市场情绪偏向看涨。然而，BeOA给出了看跌信号，主要基于苹果iPhone在中国市场份额下降等因素。这些因素可能对苹果公司的业绩产生负面影响，导致市场情绪偏向看跌。NOA认为市场当前处于中性趋势，没有明显的看涨或看跌信号。分析层智能体的NAA、TAA和FAA结果也显示了一个中性或复杂的市场情况。NAA的情绪评分和新闻摘要显示了一些正面和负面的新闻，TAA的技术分析显示市场处于中性状态，而FAA的基本面分析显示苹果公司的估值合理，财务状况良好，竞争地位领先。综合以上分析，虽然BOA和BeOA给出了相反的信号，但NOA的中性展望提供了平衡的观点。因此，最终决策是持有，以避免在市场不确定性中采取过于激进的操作。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-03'}
2025-07-25 12:58:47,968 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-25 12:58:47,977 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-25 12:58:47,977 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-25 12:58:47,977 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000, 数据: 新闻32条
2025-07-25 12:59:47,486 - __main__ - INFO - 🤖 NAA 输出: 5 个字段
2025-07-25 12:59:47,487 - __main__ - INFO - 🤖 执行智能体 TAA...
2025-07-25 12:59:47,487 - __main__ - INFO - 🤖 TAA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000, 数据: 价格30个点
2025-07-25 13:00:06,684 - __main__ - INFO - 🤖 TAA 输出: 6 个字段
2025-07-25 13:00:06,685 - __main__ - INFO - 🤖 执行智能体 FAA...
2025-07-25 13:00:06,685 - __main__ - INFO - 🤖 FAA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000, 数据: 基本面1个股票
2025-07-25 13:00:39,595 - __main__ - INFO - 🤖 FAA 输出: 13 个字段
2025-07-25 13:00:39,597 - __main__ - INFO - ✅ 分析层完成: 3 个智能体
2025-07-25 13:00:39,597 - __main__ - INFO - 🤖 执行展望层智能体 BOA...
2025-07-25 13:00:39,597 - __main__ - INFO - 🤖 BOA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000
2025-07-25 13:01:16,396 - __main__ - INFO - 🤖 BOA 输出: 5 个字段
2025-07-25 13:01:16,399 - __main__ - INFO - ✅ 展望层智能体 BOA 完成: 已输出结果
2025-07-25 13:01:16,399 - __main__ - INFO - 🤖 执行展望层智能体 BeOA...
2025-07-25 13:01:16,399 - __main__ - INFO - 🤖 BeOA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000
2025-07-25 13:02:20,633 - __main__ - INFO - 🤖 BeOA 输出: 5 个字段
2025-07-25 13:02:20,633 - __main__ - INFO - ✅ 展望层智能体 BeOA 完成: 已输出结果
2025-07-25 13:02:20,633 - __main__ - INFO - 🤖 执行展望层智能体 NOA...
2025-07-25 13:02:20,633 - __main__ - INFO - 🤖 NOA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000
2025-07-25 13:03:33,119 - __main__ - INFO - 🤖 NOA 输出: 5 个字段
2025-07-25 13:03:33,121 - __main__ - INFO - ✅ 展望层智能体 NOA 完成: 已输出结果
2025-07-25 13:03:33,121 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-25 13:03:33,121 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-06, 累计收益=0.0000, 周收益=0.0000
2025-07-25 13:03:33,121 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 3088
2025-07-25 13:03:33,121 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 13:03:33,121 - __main__ - INFO - 🔍 提示词结尾200字符: ... NAA: 分析日期: 2025-01-06\n  - TAA: 分析日期: 2025-01-0... (信心度: 未知)
  • NOA: {'analysis': "📅 分析日期: 2025-01-06\n\n🤖 分析层智能体输出:\n  - NAA: 分析日期: 2025-01-06\n  - TAA: 分析日期: 2025-01-0... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 13:03:58,726 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-25 13:03:58,728 - __main__ - INFO - 🔍 LLM响应前200字符: {'analysis': '在2025-01-06的分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示市场情绪偏向乐观，多条新闻对AAPL股价产生正面影响，尤其是关于苹果供应链和AI芯片股票的报道。TAA显示AAPL在2025-01-06呈现看涨趋势，技术指标如RSI、MACD和移动平均线均支持这一观点。FAA评估AAPL的估值合理，财务健康得分高，竞争地位领先。展望层智能体中，BO...
2025-07-25 13:03:58,728 - __main__ - INFO - 🔍 解析后结果: {'analysis': '在2025-01-06的分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示市场情绪偏向乐观，多条新闻对AAPL股价产生正面影响，尤其是关于苹果供应链和AI芯片股票的报道。TAA显示AAPL在2025-01-06呈现看涨趋势，技术指标如RSI、MACD和移动平均线均支持这一观点。FAA评估AAPL的估值合理，财务健康得分高，竞争地位领先。展望层智能体中，BOA和BeOA的信号方向相反，但强度均不强，而NOA认为应中性观望。考虑到这些信息，我们得出结论，尽管市场情绪和技术分析支持看涨，但展望层信号的不一致性使得我们倾向于保持中性观望。因此，**最终决策：持有**。'}
2025-07-25 13:03:58,729 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-25 13:03:58,729 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-25 13:03:58,729 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-25 13:03:58,729 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-25 13:03:58,729 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-25 13:03:58,729 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在2025-01-06的分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示市场情绪偏向乐观，多条新闻对AAPL股价产生正面影响，尤其是关于苹果供应链和AI芯片股票的报道。TAA显示AAPL在2025-01-06呈现看涨趋势，技术指标如RSI、MACD和移动平均线均支持这一观点。FAA评估AAPL的估值合理，财务健康得分高，竞争地位领先。展望层智能体中，BOA和BeOA的信号方向相反，但强度均不强，而NOA认为应中性观望。考虑到这些信息，我们得出结论，尽管市场情绪和技术分析支持看涨，但展望层信号的不一致性使得我们倾向于保持中性观望。因此，**最终决策：持有**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-06'}
2025-07-25 13:03:58,730 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-25 13:03:58,736 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-25 13:03:58,736 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-25 13:03:58,736 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-07, 累计收益=0.0000, 周收益=0.0000, 数据: 新闻46条
2025-07-25 13:05:32,006 - __main__ - INFO - 🤖 NAA 输出: 5 个字段
2025-07-25 13:05:32,006 - __main__ - INFO - 🤖 执行智能体 TAA...
2025-07-25 13:05:32,006 - __main__ - INFO - 🤖 TAA 输入: 日期=2025-01-07, 累计收益=0.0000, 周收益=0.0000, 数据: 价格30个点
2025-07-25 13:05:50,202 - __main__ - INFO - 🤖 TAA 输出: 6 个字段
2025-07-25 13:05:50,202 - __main__ - INFO - 🤖 执行智能体 FAA...
2025-07-25 13:05:50,202 - __main__ - INFO - 🤖 FAA 输入: 日期=2025-01-07, 累计收益=0.0000, 周收益=0.0000, 数据: 基本面1个股票
2025-07-25 13:06:22,451 - __main__ - INFO - 🤖 FAA 输出: 13 个字段
2025-07-25 13:06:22,451 - __main__ - INFO - ✅ 分析层完成: 3 个智能体
2025-07-25 13:06:22,451 - __main__ - INFO - 🤖 执行展望层智能体 BOA...
2025-07-25 13:06:22,451 - __main__ - INFO - 🤖 BOA 输入: 日期=2025-01-07, 累计收益=0.0000, 周收益=0.0000
2025-07-25 13:07:23,021 - __main__ - INFO - 🤖 BOA 输出: 5 个字段
2025-07-25 13:07:23,022 - __main__ - INFO - ✅ 展望层智能体 BOA 完成: 已输出结果
2025-07-25 13:07:23,022 - __main__ - INFO - 🤖 执行展望层智能体 BeOA...
2025-07-25 13:07:23,022 - __main__ - INFO - 🤖 BeOA 输入: 日期=2025-01-07, 累计收益=0.0000, 周收益=0.0000
2025-07-25 13:08:13,130 - __main__ - INFO - 🤖 BeOA 输出: 5 个字段
2025-07-25 13:08:13,130 - __main__ - INFO - ✅ 展望层智能体 BeOA 完成: 已输出结果
2025-07-25 13:08:13,130 - __main__ - INFO - 🤖 执行展望层智能体 NOA...
2025-07-25 13:08:13,131 - __main__ - INFO - 🤖 NOA 输入: 日期=2025-01-07, 累计收益=0.0000, 周收益=0.0000
2025-07-25 13:09:04,338 - __main__ - INFO - 🤖 NOA 输出: 5 个字段
2025-07-25 13:09:04,339 - __main__ - INFO - ✅ 展望层智能体 NOA 完成: 已输出结果
2025-07-25 13:09:04,339 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-25 13:09:04,339 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-07, 累计收益=0.0000, 周收益=0.0000
2025-07-25 13:09:04,339 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 3584
2025-07-25 13:09:04,339 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 13:09:04,339 - __main__ - INFO - 🔍 提示词结尾200字符: ...分析层智能体的输出，以下是对市场的看跌展望：\n\n- **看跌因素列表**:\n  1. 苹果... (信心度: 未知)
  • NOA: {'analysis': '📅 分析日期: 2025-01-07\n\n📈 市场展望（中性）:\n当前市场表现出一定的波动性，既有看涨因素也有看跌因素，因此市场展望为中性。\n\n📊 平衡分析:\n-... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 13:09:33,127 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-25 13:09:33,128 - __main__ - INFO - 🔍 LLM响应前200字符: {'analysis': '在本次分析中，我们综合考虑了分析层智能体的输出和展望层智能体的信号。NAA提供了对苹果公司AI新闻摘要误导性的报道，以及分析师对苹果股票的熊市观点，这些因素可能对苹果的声誉和股票价格产生负面影响。TAA显示AAPL在技术面上呈现看跌趋势，支撑位在$241.35，阻力位在$245.55。FAA评估苹果的财务健康状况良好，竞争地位领先，长期前景合理。展望层智能体中，BOA给...
2025-07-25 13:09:33,128 - __main__ - INFO - 🔍 解析后结果: {'analysis': '在本次分析中，我们综合考虑了分析层智能体的输出和展望层智能体的信号。NAA提供了对苹果公司AI新闻摘要误导性的报道，以及分析师对苹果股票的熊市观点，这些因素可能对苹果的声誉和股票价格产生负面影响。TAA显示AAPL在技术面上呈现看跌趋势，支撑位在$241.35，阻力位在$245.55。FAA评估苹果的财务健康状况良好，竞争地位领先，长期前景合理。展望层智能体中，BOA给出了看涨信号，而BeOA给出了看跌信号，NOA则认为市场展望中性。由于BOA和BeOA的信号方向相反，我们优先考虑NOA的中性展望。综合分析，我们得出结论，尽管存在一些看涨和看跌的因素，但市场整体表现出一定的波动性，因此我们倾向于持有AAPL。**最终决策：持有**。'}
2025-07-25 13:09:33,128 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-25 13:09:33,128 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-25 13:09:33,128 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-25 13:09:33,128 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-25 13:09:33,128 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-25 13:09:33,128 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合考虑了分析层智能体的输出和展望层智能体的信号。NAA提供了对苹果公司AI新闻摘要误导性的报道，以及分析师对苹果股票的熊市观点，这些因素可能对苹果的声誉和股票价格产生负面影响。TAA显示AAPL在技术面上呈现看跌趋势，支撑位在$241.35，阻力位在$245.55。FAA评估苹果的财务健康状况良好，竞争地位领先，长期前景合理。展望层智能体中，BOA给出了看涨信号，而BeOA给出了看跌信号，NOA则认为市场展望中性。由于BOA和BeOA的信号方向相反，我们优先考虑NOA的中性展望。综合分析，我们得出结论，尽管存在一些看涨和看跌的因素，但市场整体表现出一定的波动性，因此我们倾向于持有AAPL。**最终决策：持有**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-07'}
2025-07-25 13:09:33,128 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-25 13:09:33,134 - __main__ - INFO - 🔄 顺序执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-25 13:09:33,134 - __main__ - INFO - 🤖 执行智能体 NAA...
2025-07-25 13:09:33,135 - __main__ - INFO - 🤖 NAA 输入: 日期=2025-01-08, 累计收益=0.0000, 周收益=0.0000, 数据: 新闻16条
2025-07-25 13:10:13,386 - __main__ - INFO - 🤖 NAA 输出: 5 个字段
2025-07-25 13:10:13,386 - __main__ - INFO - 🤖 执行智能体 TAA...
2025-07-25 13:10:13,386 - __main__ - INFO - 🤖 TAA 输入: 日期=2025-01-08, 累计收益=0.0000, 周收益=0.0000, 数据: 价格30个点
2025-07-25 13:11:21,861 - __main__ - INFO - 🤖 TAA 输出: 6 个字段
2025-07-25 13:11:21,862 - __main__ - INFO - 🤖 执行智能体 FAA...
2025-07-25 13:11:21,862 - __main__ - INFO - 🤖 FAA 输入: 日期=2025-01-08, 累计收益=0.0000, 周收益=0.0000, 数据: 基本面1个股票
2025-07-25 13:11:54,780 - __main__ - INFO - 🤖 FAA 输出: 13 个字段
2025-07-25 13:11:54,781 - __main__ - INFO - ✅ 分析层完成: 3 个智能体
2025-07-25 13:11:54,781 - __main__ - INFO - 🤖 执行展望层智能体 BOA...
2025-07-25 13:11:54,781 - __main__ - INFO - 🤖 BOA 输入: 日期=2025-01-08, 累计收益=0.0000, 周收益=0.0000
2025-07-25 13:12:58,682 - __main__ - INFO - 🤖 BOA 输出: 5 个字段
2025-07-25 13:12:58,683 - __main__ - INFO - ✅ 展望层智能体 BOA 完成: 已输出结果
2025-07-25 13:12:58,683 - __main__ - INFO - 🤖 执行展望层智能体 BeOA...
2025-07-25 13:12:58,683 - __main__ - INFO - 🤖 BeOA 输入: 日期=2025-01-08, 累计收益=0.0000, 周收益=0.0000
2025-07-25 13:13:39,593 - __main__ - INFO - 🤖 BeOA 输出: 5 个字段
2025-07-25 13:13:39,593 - __main__ - INFO - ✅ 展望层智能体 BeOA 完成: 已输出结果
2025-07-25 13:13:39,593 - __main__ - INFO - 🤖 执行展望层智能体 NOA...
2025-07-25 13:13:39,593 - __main__ - INFO - 🤖 NOA 输入: 日期=2025-01-08, 累计收益=0.0000, 周收益=0.0000
2025-07-25 13:14:50,713 - __main__ - INFO - 🤖 NOA 输出: 5 个字段
2025-07-25 13:14:50,714 - __main__ - INFO - ✅ 展望层智能体 NOA 完成: 已输出结果
2025-07-25 13:14:50,714 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-25 13:14:50,714 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-08, 累计收益=0.0000, 周收益=0.0000
2025-07-25 13:14:50,714 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 2862
2025-07-25 13:14:50,714 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 13:14:50,714 - __main__ - INFO - 🔍 提示词结尾200字符: ... NAA: 情绪评分：0.6，市场对苹果公司（AAPL）持积极态度，对科技行业的兴趣增加，但沃伦... (信心度: 未知)
  • NOA: {'analysis': '📅 分析日期: 2025-01-08\n\n🤖 分析层智能体输出分析：\n\n- **市场展望（中性）**：市场情绪较为乐观，受到苹果公司被推荐购买和科技行业兴趣的提升的推... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 13:15:14,747 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-25 13:15:14,748 - __main__ - INFO - 🔍 LLM响应前200字符: {'analysis': '今日分析结果显示，市场对苹果公司（AAPL）持积极态度，尤其是由于苹果被推荐为2025年值得购买的股票之一，这为苹果股票带来了正面影响。然而，技术分析显示，AAPL的股价呈现下跌趋势，技术评分较低，表明短期内可能存在下跌风险。基本面分析显示，苹果公司的估值合理，财务健康得分较高，竞争地位领先，长期趋势看好。展望层智能体中，BOA和BeOA给出了相反的信号，BOA看涨，而...
2025-07-25 13:15:14,748 - __main__ - INFO - 🔍 解析后结果: {'analysis': '今日分析结果显示，市场对苹果公司（AAPL）持积极态度，尤其是由于苹果被推荐为2025年值得购买的股票之一，这为苹果股票带来了正面影响。然而，技术分析显示，AAPL的股价呈现下跌趋势，技术评分较低，表明短期内可能存在下跌风险。基本面分析显示，苹果公司的估值合理，财务健康得分较高，竞争地位领先，长期趋势看好。展望层智能体中，BOA和BeOA给出了相反的信号，BOA看涨，而BeOA看跌。NOA认为市场应保持中性观望。考虑到这些信息，尽管市场情绪和技术分析存在分歧，但苹果公司的基本面表现良好，因此倾向于执行买入操作。**最终决策：买入**'}
2025-07-25 13:15:14,748 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-25 13:15:14,748 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-25 13:15:14,748 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-25 13:15:14,748 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-25 13:15:14,748 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-25 13:15:14,748 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-25 13:15:14,748 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-25 13:15:14,748 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析结果显示，市场对苹果公司（AAPL）持积极态度，尤其是由于苹果被推荐为2025年值得购买的股票之一，这为苹果股票带来了正面影响。然而，技术分析显示，AAPL的股价呈现下跌趋势，技术评分较低，表明短期内可能存在下跌风险。基本面分析显示，苹果公司的估值合理，财务健康得分较高，竞争地位领先，长期趋势看好。展望层智能体中，BOA和BeOA给出了相反的信号，BOA看涨，而BeOA看跌。NOA认为市场应保持中性观望。考虑到这些信息，尽管市场情绪和技术分析存在分歧，但苹果公司的基本面表现良好，因此倾向于执行买入操作。**最终决策：买入**', 'action': 'buy', 'trading_actions': {'AAPL': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-08'}
2025-07-25 13:15:14,748 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': 1.0}
2025-07-25 13:15:14,748 - __main__ - INFO - 📈 第5天交易动作: {'AAPL': 1.0}
2025-07-25 13:15:14,753 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0129, 净值=$987101.74
2025-07-25 13:15:14,753 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-25 13:15:14,753 - __main__ - INFO - ============================================================
2025-07-25 13:15:14,753 - __main__ - INFO - 第 1 周性能分析 - 联盟: frozenset({'NOA', 'NAA', 'FAA', 'BOA', 'TRA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,753 - __main__ - INFO - 周总收益率: -0.0129
2025-07-25 13:15:14,753 - __main__ - INFO - 周夏普比率: -8.1814
2025-07-25 13:15:14,753 - __main__ - INFO - 交易天数: 5
2025-07-25 13:15:14,753 - __main__ - INFO - ============================================================
2025-07-25 13:15:14,753 - __main__ - INFO - 📅 保持当前周数: 第1周 (stop_after_one_week=True)
2025-07-25 13:15:14,753 - __main__ - INFO - 🔄 5日循环模式：第0周完成，停止模拟
2025-07-25 13:15:14,753 - __main__ - INFO - 📊 周期边界: 当前周=0, 下一周=1, 交易日=4
2025-07-25 13:15:14,753 - __main__ - INFO - 📊 Sharpe计算调试:
2025-07-25 13:15:14,753 - __main__ - INFO -   收益率数组长度: 5
2025-07-25 13:15:14,754 - __main__ - INFO -   收益率范围: -0.012898 ~ 0.000000
2025-07-25 13:15:14,754 - __main__ - INFO -   收益率均值: -0.002580
2025-07-25 13:15:14,754 - __main__ - INFO -   收益率标准差: 0.005159
2025-07-25 13:15:14,754 - __main__ - INFO -   年化收益率: -0.650072
2025-07-25 13:15:14,754 - __main__ - INFO -   年化波动率: 0.081901
2025-07-25 13:15:14,754 - __main__ - INFO -   最终Sharpe比率: -8.181450
2025-07-25 13:15:14,754 - __main__ - INFO - ✅ 联盟 frozenset({'NOA', 'NAA', 'FAA', 'BOA', 'TRA', 'BeOA', 'TAA'}) 模拟完成:
2025-07-25 13:15:14,754 - __main__ - INFO -   📊 夏普比率: -8.181450
2025-07-25 13:15:14,754 - __main__ - INFO -   📈 日收益率数量: 5
2025-07-25 13:15:14,754 - __main__ - INFO -   🕐 模拟时间: 1569.30s
2025-07-25 13:15:14,755 - __main__ - INFO - 联盟模拟完成: {'NOA', 'NAA', 'FAA', 'BOA', 'TRA', 'BeOA', 'TAA'}, 夏普比率=-8.1814, 耗时=1569.298s
2025-07-25 13:15:14,755 - __main__ - INFO - ✅ 完整联盟模拟完成: 夏普比率 = -8.1814
2025-07-25 13:15:14,755 - __main__ - INFO - ======================================================================
2025-07-25 13:15:14,755 - __main__ - INFO - ⚡ 阶段2: 子集联盟快速模拟
2025-07-25 13:15:14,755 - __main__ - INFO - ======================================================================
2025-07-25 13:15:14,755 - __main__ - INFO - 📝 启用简洁日志模式 - 只显示联盟组合和结果
2025-07-25 13:15:14,755 - __main__ - INFO - 🚀 启用并发执行: 49 个子集联盟
2025-07-25 13:15:14,755 - __main__ - INFO - 🚀 开始并发执行 49 个联盟...
2025-07-25 13:15:14,756 - __main__ - INFO - 开始模拟联盟: {'TRA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,756 - __main__ - INFO - 开始模拟联盟: {'NOA', 'TRA', 'NAA', 'FAA'}
2025-07-25 13:15:14,756 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,756 - __main__ - INFO - 开始模拟联盟: {'TRA', 'NOA', 'TAA'}
2025-07-25 13:15:14,756 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,756 - __main__ - INFO - 开始模拟联盟: {'NAA', 'FAA', 'TRA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,756 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,756 - __main__ - INFO - 开始模拟联盟: {'NOA', 'BOA', 'NAA', 'TRA', 'BeOA'}
2025-07-25 13:15:14,756 - __main__ - INFO - 开始模拟联盟: {'NOA', 'NAA', 'TRA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,756 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,756 - __main__ - INFO - 开始模拟联盟: {'BOA', 'FAA', 'TRA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,756 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'TRA', 'NAA', 'FAA'})
2025-07-25 13:15:14,757 - __main__ - INFO - 开始模拟联盟: {'TRA', 'NAA', 'FAA'}
2025-07-25 13:15:14,757 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,757 - __main__ - INFO - 开始模拟联盟: {'NAA', 'BOA', 'TRA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,758 - __main__ - INFO - 开始模拟联盟: {'NOA', 'FAA', 'NAA', 'TRA', 'BeOA'}
2025-07-25 13:15:14,758 - __main__ - INFO - 开始模拟联盟: {'NOA', 'FAA', 'BOA', 'TRA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,758 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,758 - __main__ - INFO - 开始模拟联盟: {'TRA', 'NAA', 'FAA', 'TAA'}
2025-07-25 13:15:14,758 - __main__ - INFO - 开始模拟联盟: {'TRA', 'BOA', 'NAA'}
2025-07-25 13:15:14,758 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,758 - __main__ - INFO - 开始模拟联盟: {'NOA', 'FAA', 'BOA', 'NAA', 'TRA', 'TAA'}
2025-07-25 13:15:14,758 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'NOA', 'TAA'})
2025-07-25 13:15:14,758 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TRA', 'NAA', 'BeOA'}
2025-07-25 13:15:14,759 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,759 - __main__ - INFO - 开始模拟联盟: {'BOA', 'FAA', 'NAA', 'TRA', 'TAA'}
2025-07-25 13:15:14,759 - __main__ - INFO - 开始模拟联盟: {'TRA', 'NAA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,760 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TRA', 'NOA', 'BeOA'}
2025-07-25 13:15:14,760 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,760 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NAA', 'FAA', 'TRA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,760 - __main__ - INFO - 开始模拟联盟: {'NOA', 'TRA', 'BOA', 'FAA'}
2025-07-25 13:15:14,760 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,760 - __main__ - INFO - 开始模拟联盟: {'NOA', 'TRA', 'BOA', 'NAA'}
2025-07-25 13:15:14,760 - __main__ - INFO - 开始模拟联盟: {'TRA', 'BOA', 'FAA', 'TAA'}
2025-07-25 13:15:14,761 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,761 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,761 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TRA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,761 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'BOA', 'NAA', 'TRA', 'BeOA'})
2025-07-25 13:15:14,761 - __main__ - INFO - 开始模拟联盟: {'NOA', 'FAA', 'NAA', 'TRA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,761 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,761 - __main__ - INFO - 开始模拟联盟: {'TRA', 'NAA', 'BeOA'}
2025-07-25 13:15:14,761 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,761 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TRA', 'BeOA'}
2025-07-25 13:15:14,762 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'NAA', 'TRA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,762 - __main__ - INFO - 开始模拟联盟: {'NAA', 'FAA', 'BOA', 'TRA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,762 - __main__ - INFO - 开始模拟联盟: {'TRA', 'BOA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,762 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,762 - __main__ - INFO - 开始模拟联盟: {'NAA', 'NOA', 'BOA', 'TRA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,762 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,763 - __main__ - INFO - 开始联盟交易模拟: frozenset({'BOA', 'FAA', 'TRA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,763 - __main__ - INFO - 开始模拟联盟: {'TRA', 'NOA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,763 - __main__ - INFO - 开始模拟联盟: {'NOA', 'FAA', 'TRA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,763 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,763 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,763 - __main__ - INFO - 开始模拟联盟: {'NOA', 'BOA', 'FAA', 'TRA', 'TAA'}
2025-07-25 13:15:14,763 - __main__ - INFO - 开始模拟联盟: {'TRA', 'NAA', 'TAA'}
2025-07-25 13:15:14,763 - __main__ - INFO - 开始模拟联盟: {'TRA', 'BOA', 'FAA', 'NAA'}
2025-07-25 13:15:14,764 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,764 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'NAA', 'FAA'})
2025-07-25 13:15:14,764 - __main__ - INFO - 开始模拟联盟: {'TRA', 'BOA', 'TAA'}
2025-07-25 13:15:14,764 - __main__ - INFO - 开始模拟联盟: {'NOA', 'FAA', 'BOA', 'TRA', 'BeOA'}
2025-07-25 13:15:14,764 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,764 - __main__ - INFO - 开始模拟联盟: {'NOA', 'TRA', 'NAA'}
2025-07-25 13:15:14,765 - __main__ - INFO - 开始模拟联盟: {'BOA', 'FAA', 'NAA', 'TRA', 'BeOA'}
2025-07-25 13:15:14,765 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NAA', 'BOA', 'TRA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,765 - __main__ - INFO - 开始模拟联盟: {'NOA', 'BOA', 'NAA', 'TRA', 'TAA'}
2025-07-25 13:15:14,765 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,765 - __main__ - INFO - 开始模拟联盟: {'NOA', 'FAA', 'BOA', 'NAA', 'TRA', 'BeOA'}
2025-07-25 13:15:14,765 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,765 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TRA', 'BOA', 'BeOA'}
2025-07-25 13:15:14,765 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'FAA', 'NAA', 'TRA', 'BeOA'})
2025-07-25 13:15:14,766 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'FAA', 'BOA', 'TRA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,766 - __main__ - INFO - 开始模拟联盟: {'NOA', 'TRA', 'BOA', 'TAA'}
2025-07-25 13:15:14,766 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,766 - __main__ - INFO - 开始模拟联盟: {'NOA', 'NAA', 'FAA', 'TRA', 'TAA'}
2025-07-25 13:15:14,766 - __main__ - INFO - 开始模拟联盟: {'NOA', 'TRA', 'NAA', 'TAA'}
2025-07-25 13:15:14,767 - __main__ - INFO - 开始模拟联盟: {'TRA', 'NOA', 'FAA', 'TAA'}
2025-07-25 13:15:14,767 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,768 - __main__ - INFO - 开始模拟联盟: {'TRA', 'BOA', 'NAA', 'TAA'}
2025-07-25 13:15:14,768 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'NAA', 'FAA', 'TAA'})
2025-07-25 13:15:14,768 - __main__ - INFO - 开始模拟联盟: {'NOA', 'BOA', 'TRA', 'BeOA', 'TAA'}
2025-07-25 13:15:14,768 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,768 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'BOA', 'NAA'})
2025-07-25 13:15:14,768 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,769 - __main__ - INFO - 开始模拟联盟: {'TRA', 'BOA', 'BeOA', 'NAA'}
2025-07-25 13:15:14,769 - __main__ - INFO - 开始模拟联盟: {'NOA', 'BOA', 'FAA', 'NAA', 'TRA'}
2025-07-25 13:15:14,769 - __main__ - INFO - 开始模拟联盟: {'NOA', 'TRA', 'NAA', 'BeOA'}
2025-07-25 13:15:14,769 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,769 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,769 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'FAA', 'BOA', 'NAA', 'TRA', 'TAA'})
2025-07-25 13:15:14,770 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,770 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TRA', 'NAA', 'BeOA'})
2025-07-25 13:15:14,771 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,771 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,771 - __main__ - INFO - 开始联盟交易模拟: frozenset({'BOA', 'FAA', 'NAA', 'TRA', 'TAA'})
2025-07-25 13:15:14,771 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'NAA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,772 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,772 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,773 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,773 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TRA', 'NOA', 'BeOA'})
2025-07-25 13:15:14,774 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,774 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,774 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'TRA', 'BOA', 'FAA'})
2025-07-25 13:15:14,775 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,775 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,776 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,777 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'TRA', 'BOA', 'NAA'})
2025-07-25 13:15:14,777 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,778 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'BOA', 'FAA', 'TAA'})
2025-07-25 13:15:14,778 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,780 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,780 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TRA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,780 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,781 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,781 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,781 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'FAA', 'NAA', 'TRA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,782 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,785 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,786 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'NAA', 'BeOA'})
2025-07-25 13:15:14,806 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TRA', 'BeOA'})
2025-07-25 13:15:14,812 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,818 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,823 - __main__ - INFO - 📅 当前周数: 1
2025-07-25 13:15:14,825 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NAA', 'FAA', 'BOA', 'TRA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,826 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'BOA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,827 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NAA', 'NOA', 'BOA', 'TRA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,830 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'NOA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,848 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'FAA', 'TRA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,850 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'BOA', 'FAA', 'TRA', 'TAA'})
2025-07-25 13:15:14,850 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'NAA', 'TAA'})
2025-07-25 13:15:14,850 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'BOA', 'FAA', 'NAA'})
2025-07-25 13:15:14,851 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'BOA', 'TAA'})
2025-07-25 13:15:14,852 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'FAA', 'BOA', 'TRA', 'BeOA'})
2025-07-25 13:15:14,853 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'TRA', 'NAA'})
2025-07-25 13:15:14,854 - __main__ - INFO - 开始联盟交易模拟: frozenset({'BOA', 'FAA', 'NAA', 'TRA', 'BeOA'})
2025-07-25 13:15:14,854 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'BOA', 'NAA', 'TRA', 'TAA'})
2025-07-25 13:15:14,855 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'FAA', 'BOA', 'NAA', 'TRA', 'BeOA'})
2025-07-25 13:15:14,856 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TRA', 'BOA', 'BeOA'})
2025-07-25 13:15:14,857 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'TRA', 'BOA', 'TAA'})
2025-07-25 13:15:14,859 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'NAA', 'FAA', 'TRA', 'TAA'})
2025-07-25 13:15:14,861 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'TRA', 'NAA', 'TAA'})
2025-07-25 13:15:14,862 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'NOA', 'FAA', 'TAA'})
2025-07-25 13:15:14,881 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'BOA', 'NAA', 'TAA'})
2025-07-25 13:15:14,882 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'BOA', 'TRA', 'BeOA', 'TAA'})
2025-07-25 13:15:14,885 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TRA', 'BOA', 'BeOA', 'NAA'})
2025-07-25 13:15:14,886 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'BOA', 'FAA', 'NAA', 'TRA'})
2025-07-25 13:15:14,887 - __main__ - INFO - 开始联盟交易模拟: frozenset({'NOA', 'TRA', 'NAA', 'BeOA'})
2025-07-25 13:16:04,041 - __main__ - ERROR - LLM分析请求失败: Error code: 429, with error text {"error":{"code":"1302","message":"您当前使用该API的并发数过高，请降低并发，或联系客服增加限额。"}}
2025-07-25 13:16:04,043 - __main__ - WARNING - 🤖 BOA: 数据验证失败，使用默认响应
2025-07-25 13:16:04,043 - __main__ - ERROR - 智能体 BOA LLM调用失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-25 13:16:31,917 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 1893
2025-07-25 13:16:31,918 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 13:16:31,918 - __main__ - INFO - 🔍 提示词结尾200字符: ...分析信心度：0.9

总结：整体来看，市场情绪较为乐观，但苹果的销量预测下调可能会对股价产生负面影响。 (信心度: 未知)
  • FAA: {'valuation_assessment': '合理', 'financial_health_score': 8.5, 'competitive_position': '领先', 'long_te... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 13:16:38,509 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 1326
2025-07-25 13:16:38,509 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 13:16:38,509 - __main__ - INFO - 🔍 提示词结尾200字符: ...petitive_position': '领先', 'long_te... (信心度: 未知)

🔮 展望层智能体输出:
  • BeOA: {'analysis': "📅 分析日期: 2025-01-02\n\n🤖 分析层智能体输出:\n  • FAA: {'valuation_assessment': '合理', 'financial_... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 13:16:40,747 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 1905
2025-07-25 13:16:40,747 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 13:16:40,748 - __main__ - INFO - 🔍 提示词结尾200字符: ...：0.9
  - 分析基于22条新闻，涵盖了多个方面，因此具有较高的信心度。 (信心度: 未知)

🔮 展望层智能体输出:
  • BOA: {'analysis': '📅 分析日期: 2025-01-02\n\n🤖 看涨展望结果：\n\n**市场展望：看涨**\n\n**看涨因素列表：**\n1. **科技巨头积极展望**：多位科技巨头，... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 13:16:48,444 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 2691
2025-07-25 13:16:48,445 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 13:16:48,445 - __main__ - INFO - 🔍 提示词结尾200字符: ...指标分析：
     - RSI：需要具体数值，但可能低于50，表明超卖。
     - MACD：需要具体数值，但可能显示看跌信号。
     - 移动平均线：需要具体数值，但可能显示短期均线下穿长期均线。
   - 分析信心度：0.8（基于当前趋势和动量分析）

请注意，上述分析是基于提供的历史数据和趋势分析，具体的技术指标值需要进一步计算。 (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 13:16:56,152 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 1906
2025-07-25 13:16:56,152 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 13:16:56,153 - __main__ - INFO - 🔍 提示词结尾200字符: ...0.9
  - 分析基于22条新闻，涵盖了多个方面，因此具有较高的信心度。 (信心度: 未知)

🔮 展望层智能体输出:
  • BeOA: {'analysis': '📅 分析日期: 2025-01-02\n\n🤖 看跌展望结果：\n\n**市场展望（看跌）**\n- 基于当前的市场情绪和关键事件，市场展望为看跌。尽管有科技巨头发布积极展... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 13:17:00,082 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-25 13:17:00,082 - __main__ - INFO - 🔍 LLM响应前200字符: {'analysis': '分析日期为2025-01-02，当前持仓为空。分析层智能体FAA的输出显示，公司的估值评估为合理，财务健康得分为8.5，竞争地位领先，长期趋势分析结果未知。展望层智能体BeOA的输出信息缺失，无法进行具体分析。由于缺乏具体的展望层智能体信号，我们无法根据信号强度和方向做出决策。考虑到分析层智能体的输出提供了积极的财务和竞争信息，但缺乏明确的展望层信号，我们选择持有当前仓...
2025-07-25 13:17:00,083 - __main__ - INFO - 🔍 解析后结果: {'analysis': '分析日期为2025-01-02，当前持仓为空。分析层智能体FAA的输出显示，公司的估值评估为合理，财务健康得分为8.5，竞争地位领先，长期趋势分析结果未知。展望层智能体BeOA的输出信息缺失，无法进行具体分析。由于缺乏具体的展望层智能体信号，我们无法根据信号强度和方向做出决策。考虑到分析层智能体的输出提供了积极的财务和竞争信息，但缺乏明确的展望层信号，我们选择持有当前仓位。**最终决策：持有**'}
2025-07-25 13:17:00,083 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-25 13:17:02,251 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 2085
2025-07-25 13:17:02,251 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 13:17:02,251 - __main__ - INFO - 🔍 提示词结尾200字符: ...处理。 (信心度: 未知)

🔮 展望层智能体输出:
  • BOA: LLM不可用，使用默认分析 (信心度: 0.5)
  • BeOA: {'analysis': '📅 分析日期: 2025-01-02\n\n🤖 分析层智能体输出:\n  • TAA: 分析日期: 2025-01-02\n\n📈 股票价格历史分析：\n\n基于TAA的分... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 13:17:05,892 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 1905
2025-07-25 13:17:05,893 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 13:17:05,893 - __main__ - INFO - 🔍 提示词结尾200字符: ...：0.9
  - 分析基于22条新闻，涵盖了多个方面，因此具有较高的信心度。 (信心度: 未知)

🔮 展望层智能体输出:
  • NOA: {'analysis': '📅 分析日期: 2025-01-02\n\n🤖 中性展望结果：\n\n**市场展望（中性）：**\n当前市场展现出一定的乐观情绪，但同时也存在不确定性。科技巨头的积极展望和... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 13:17:08,351 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-25 13:17:08,352 - __main__ - INFO - 🔍 LLM响应前200字符: {'analysis': '在分析层智能体的输出中，NAA提供了关于苹果公司及其市场的全面分析。情绪评分0.6表明市场情绪相对中性，但新闻摘要中提到了苹果在中国市场的降价策略，这可能会对市场份额产生积极影响。然而，瑞士信贷银行下调了iPhone销量预测，这可能会对苹果的股价产生负面影响。展望层智能体中，BeOA给出了看跌展望，这表明市场预期苹果的股价可能会下降。尽管如此，由于NAA的分析信心度较高...
2025-07-25 13:17:08,352 - __main__ - INFO - 🔍 解析后结果: {'analysis': '在分析层智能体的输出中，NAA提供了关于苹果公司及其市场的全面分析。情绪评分0.6表明市场情绪相对中性，但新闻摘要中提到了苹果在中国市场的降价策略，这可能会对市场份额产生积极影响。然而，瑞士信贷银行下调了iPhone销量预测，这可能会对苹果的股价产生负面影响。展望层智能体中，BeOA给出了看跌展望，这表明市场预期苹果的股价可能会下降。尽管如此，由于NAA的分析信心度较高，我们应考虑这一信号。在决策层，我们需要根据展望层智能体的信号强度和方向来做出决策。由于BeOA给出了看跌信号，我们倾向于执行卖出操作。最终决策：卖出。'}
2025-07-25 13:17:08,352 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-25 13:17:08,352 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-25 13:17:08,352 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-25 13:17:08,621 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-25 13:17:08,621 - __main__ - INFO - 🔍 LLM响应前200字符: {'analysis': '在本次分析中，我们首先考虑了展望层智能体的输出。BOA给出了看涨信号，这表明市场整体趋势偏向乐观。具体来看，多位科技巨头，包括苹果公司的CEO Tim Cook，在新年伊始通过社交媒体发布了积极的展望，这为市场注入了信心。尽管瑞士信贷银行下调了苹果的iPhone销量预测，但这一影响被科技巨头的积极展望所抵消。此外，苹果在中国市场对iPhone 16系列进行了降价，以应对...
2025-07-25 13:17:08,621 - __main__ - INFO - 🔍 解析后结果: {'analysis': '在本次分析中，我们首先考虑了展望层智能体的输出。BOA给出了看涨信号，这表明市场整体趋势偏向乐观。具体来看，多位科技巨头，包括苹果公司的CEO Tim Cook，在新年伊始通过社交媒体发布了积极的展望，这为市场注入了信心。尽管瑞士信贷银行下调了苹果的iPhone销量预测，但这一影响被科技巨头的积极展望所抵消。此外，苹果在中国市场对iPhone 16系列进行了降价，以应对华为的竞争，这一策略可能会对市场份额产生积极影响。然而，BeOA没有给出明确的信号，而NOA认为应该中性观望。考虑到BOA的强烈看涨信号，我们倾向于执行买入操作。同时，分析层的NAA、TAA、FAA结果作为风险评估的参考，但不是决策主导因素。因此，**最终决策：买入**。'}
2025-07-25 13:17:08,621 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-25 13:17:08,621 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-25 13:17:08,622 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-25 13:17:08,718 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-25 13:17:08,718 - __main__ - INFO - 🔍 LLM响应前200字符: {'analysis': '分析过程如下：展望层智能体中，BOA给出了积极的展望，但并非强烈看涨；BeOA则给出了中性展望；NOA认为应该中性观望。在分析层智能体中，NAA显示市场情绪较为乐观，但苹果的销量预测下调可能会对股价产生负面影响；TAA显示短期趋势可能为看跌。综合考虑，虽然市场情绪较为乐观，但苹果的销量预测下调和技术分析显示的看跌趋势使得我们无法确定明确的买入或卖出信号。因此，根据决策规...
2025-07-25 13:17:08,718 - __main__ - INFO - 🔍 解析后结果: {'analysis': '分析过程如下：展望层智能体中，BOA给出了积极的展望，但并非强烈看涨；BeOA则给出了中性展望；NOA认为应该中性观望。在分析层智能体中，NAA显示市场情绪较为乐观，但苹果的销量预测下调可能会对股价产生负面影响；TAA显示短期趋势可能为看跌。综合考虑，虽然市场情绪较为乐观，但苹果的销量预测下调和技术分析显示的看跌趋势使得我们无法确定明确的买入或卖出信号。因此，根据决策规则，我们优先考虑NOA的建议，选择中性观望。**最终决策：持有**。'}
2025-07-25 13:17:08,718 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-25 13:17:09,826 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 2246
2025-07-25 13:17:09,826 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 13:17:09,827 - __main__ - INFO - 🔍 提示词结尾200字符: ...mpetitive_position': '领先', 'long_te... (信心度: 未知)

🔮 展望层智能体输出:
  • NOA: {'analysis': '📅 分析日期: 2025-01-02\n\n🤖 分析层智能体输出:\n  • TAA: 分析日期: 2025-01-02\n\n📈 股票价格历史分析：\n\n市场展望（中性... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 13:17:12,684 - __main__ - INFO - 🔍 LLM原始响应类型: <class 'dict'>
2025-07-25 13:17:12,684 - __main__ - INFO - 🔍 LLM响应前200字符: {'analysis': '分析过程如下：\n\n展望层智能体信号分析：\n- BOA（看涨展望）：苹果公司的CEO Tim Cook等科技巨头发布积极的新年展望，瑞士信贷银行维持中性评级，市场分析师预测股市驱动力，这些都表明市场情绪较为乐观，因此BOA给出了看涨信号。\n- BeOA（看跌展望）：苹果在中国市场面临市场份额下降的挑战，瑞士信贷银行下调了苹果的iPhone销量预测，这些因素可能导致...
2025-07-25 13:17:12,684 - __main__ - INFO - 🔍 解析后结果: {'analysis': '分析过程如下：\n\n展望层智能体信号分析：\n- BOA（看涨展望）：苹果公司的CEO Tim Cook等科技巨头发布积极的新年展望，瑞士信贷银行维持中性评级，市场分析师预测股市驱动力，这些都表明市场情绪较为乐观，因此BOA给出了看涨信号。\n- BeOA（看跌展望）：苹果在中国市场面临市场份额下降的挑战，瑞士信贷银行下调了苹果的iPhone销量预测，这些因素可能导致BeOA给出看跌信号。\n- NOA（中性展望）：由于BOA和BeOA的信号方向相反，NOA可能会建议中性观望。\n\n决策理由：\n- 虽然BOA和BeOA的信号方向相反，但考虑到市场整体情绪较为乐观，且苹果在技术硬件、存储和外围设备行业中表现尚可，我们倾向于相信BOA的看涨信号。\n- 瑞士信贷银行的下调预测可能对股价产生负面影响，但考虑到苹果的降价策略可能对市场份额产生积极影响，我们决定不优先考虑BeOA的看跌信号。\n\n风险评估：\n- 苹果在中国市场的竞争和市场份额下降的风险需要关注。\n- 瑞士信贷银行的下调预测可能对股价产生负面影响。\n\n最终决策：买入\n\n'}
2025-07-25 13:17:12,685 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-25 13:17:12,685 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-25 13:17:12,685 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-25 13:17:16,121 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 2028
2025-07-25 13:17:16,121 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 13:17:16,121 - __main__ - INFO - 🔍 提示词结尾200字符: ...mpetitive_position': '领先', 'long_te... (信心度: 未知)

🔮 展望层智能体输出:
  • BOA: {'analysis': '📅 分析日期: 2025-01-02\n\n🤖 分析层智能体输出分析：\n\n1. **市场展望（看涨）**：\n   - 根据NAA的分析结果，市场情绪评分较高，情绪评分... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 13:17:24,596 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 2123
2025-07-25 13:17:24,596 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 13:17:24,597 - __main__ - INFO - 🔍 提示词结尾200字符: ...意，上述分析是基于提供的历史数据和趋势分析，具体的技术指标值需要进一步计算。 (信心度: 未知)

🔮 展望层智能体输出:
  • NOA: {'analysis': '📅 分析日期: 2025-01-02\n\n🤖 分析层智能体输出:\n  • TAA: 分析日期: 2025-01-02\n\n📈 股票价格历史分析：\n\n市场展望（中性... (信心度: 未知)

请严格按照JSON格式回答。
2025-07-25 13:17:25,091 - __main__ - INFO - 🔍 发送给LLM的提示词长度: 1448
2025-07-25 13:17:25,091 - __main__ - INFO - 🔍 提示词开头200字符: 你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：主要基于展望层智能体的输出进行决策**

展望层决策权重分...
2025-07-25 13:17:25,092 - __main__ - INFO - 🔍 提示词结尾200字符: ... FAA: {'valuation_assessment': '合理', 'financial_... (信心度: 未知)
  • NOA: {'analysis': "📅 分析日期: 2025-01-02\n\n🤖 分析层智能体输出:\n  • FAA: {'valuation_assessment': '合理', 'financial_... (信心度: 未知)

请严格按照JSON格式回答。
