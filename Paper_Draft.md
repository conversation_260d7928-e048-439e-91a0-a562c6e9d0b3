### Introduction

The advent of powerful Large Language Models (LLMs) has catalyzed a paradigm shift in the development of Multi-Agent Systems (MAS). Teams of specialized LLM-based agents are now being deployed to tackle complex, collaborative tasks that were previously intractable, ranging from automated software development and scientific discovery to sophisticated financial analysis (<PERSON> et al. 2024). As these agent teams grow in complexity and capability, a critical challenge emerges: understanding and optimizing the collaborative dynamics that govern their collective performance.

A fundamental aspect of this challenge is the problem of credit assignment: accurately discerning the contribution of each individual agent to a team's overall success or failure. The Shapley value (<PERSON><PERSON><PERSON><PERSON> 1953), a concept from cooperative game theory, offers a provably fair and axiomatically sound solution for attributing value. However, its computational complexity of O(2^N) for a team of N agents renders it intractable for all but the smallest teams (<PERSON><PERSON> & Díaz-Rodríguez 2022). **Its computational demands make it infeasible for** the real-time, online evaluation required by dynamic agent systems, a well-documented challenge that has prompted research into various approximation methods (<PERSON> et al. 2021).

Even if credit could be assigned perfectly and efficiently, a second, equally critical challenge emerges: how can a system autonomously act on this information to improve itself? The current state-of-the-art for enhancing agent performance relies heavily on manual prompt engineering. This process is a significant bottleneck—it is unscalable, slow, and creates an unsustainable reliance on constant human expertise to diagnose and rectify the behavior of underperforming agents, a situation some have termed the 'promptware crisis' (<PERSON> et al. 2025). The ad-hoc, trial-and-error nature of prompt development highlights the need for more systematic and automated approaches (Schulhoff et al. 2024).

To address these dual challenges, we introduce the **DAG-Shapley self-adaptive framework**, an integrated solution for efficient attribution and autonomous improvement in MAS. Our framework is built upon two primary innovations. The first is a structurally-aware contribution attribution algorithm, which we also name **DAG-Shapley**, that leverages the inherent Directed Acyclic Graph (DAG) structure of agent workflows. By identifying and evaluating only viable agent coalitions, it deterministically prunes the vast search space, dramatically reducing computational cost without resorting to the stochastic approximation common in other approaches (cf. Li et al. 2021). The second innovation is a **Contribution-Guided Online Prompt Optimization (CG-OPO)** mechanism. This closed-loop process uses the precise attribution scores from DAG-Shapley to identify the weakest agent and then guides an LLM-driven reflection process to autonomously refine its operational prompts, enabling targeted, loss-oriented self-improvement.

Our primary contributions are as follows:

- We introduce **DAG-Shapley**, a novel, deterministic algorithm for contribution attribution in DAG-structured MAS that significantly reduces computational overhead while maintaining high accuracy compared to the classical Shapley value.
- We design and implement **CG-OPO**, a closed-loop online optimization process that uses attribution scores to guide the refinement of underperforming agents' prompts through reflective, loss-oriented analysis of their past failures.
- We present a complete, modular **self-adaptive framework** that integrates these components, providing a robust and extensible blueprint for building continuously improving agent teams.
- We demonstrate the framework's effectiveness in a complex multi-agent financial trading environment, showing substantial improvements in both attribution efficiency and overall task performance over static baselines.



```
@article{angelotti2023towards,
  title={Towards a more efficient computation of individual attribute and policy contribution for post-hoc explanation of cooperative multi-agent systems using Myerson values},
  author={Angelotti, Giorgio and D{\'\i}az-Rodr{\'\i}guez, Natalia},
  journal={Knowledge-Based Systems},
  volume={260},
  pages={110189},
  year={2023},
  publisher={Elsevier}
}
```

```
@article{chen2025promptware,
  title={Promptware engineering: Software engineering for llm prompt development},
  author={Chen, Zhenpeng and Wang, Chong and Sun, Weisong and Yang, Guang and Liu, Xuanzhe and Zhang, Jie M and Liu, Yang},
  journal={arXiv preprint arXiv:2503.02400},
  year={2025}
}
```

```
@article{he2025llm,
  title={LLM-Based Multi-Agent Systems for Software Engineering: Literature Review, Vision, and the Road Ahead},
  author={He, Junda and Treude, Christoph and Lo, David},
  journal={ACM Transactions on Software Engineering and Methodology},
  volume={34},
  number={5},
  pages={1--30},
  year={2025},
  publisher={ACM New York, NY}
}
```

```
@inproceedings{li2021shapley,
  title={Shapley counterfactual credits for multi-agent reinforcement learning},
  author={Li, Jiahui and Kuang, Kun and Wang, Baoxiang and Liu, Furui and Chen, Long and Wu, Fei and Xiao, Jun},
  booktitle={Proceedings of the 27th ACM SIGKDD Conference on Knowledge Discovery \& Data Mining},
  pages={934--942},
  year={2021}
}
```

```
@article{quan2024invagent,
  title={Invagent: A large language model based multi-agent system for inventory management in supply chains},
  author={Quan, Yinzhu and Liu, Zefang},
  journal={arXiv preprint arXiv:2407.11384},
  year={2024}
}
```

```
@article{murthy2025promptomatix,
  title={Promptomatix: An Automatic Prompt Optimization Framework for Large Language Models},
  author={Murthy, Rithesh and Zhu, Ming and Yang, Liangwei and Qiu, Jielin and Tan, Juntao and Heinecke, Shelby and Wang, Huan and Xiong, Caiming and Savarese, Silvio},
  journal={arXiv preprint arXiv:2507.14241},
  year={2025}
}
```

```
@article{schulhoff2024prompt,
  title={The prompt report: a systematic survey of prompt engineering techniques},
  author={Schulhoff, Sander and Ilie, Michael and Balepur, Nishant and Kahadze, Konstantine and Liu, Amanda and Si, Chenglei and Li, Yinheng and Gupta, Aayush and Han, HyoJung and Schulhoff, Sevien and others},
  journal={arXiv preprint arXiv:2406.06608},
  year={2024}
}
```

```
@article{shapley1953value,
  title={A value for n-person games},
  author={Shapley, Lloyd S and others},
  year={1953},
  publisher={Princeton University Press Princeton}
}
```

```
@article{wang2025sequential,
  title={A sequential optimal learning approach to automated prompt engineering in large language models},
  author={Wang, Shuyang and Moazeni, Somayeh and Klabjan, Diego},
  journal={arXiv preprint arXiv:2501.03508},
  year={2025}
}
```

```
@article{yang2024llm,
  title={LLM-based Multi-Agent Systems: Techniques and Business Perspectives},
  author={Yang, Yingxuan and Peng, Qiuying and Wang, Jun and Wen, Ying and Zhang, Weinan},
  journal={arXiv preprint arXiv:2411.14033},
  year={2024}
}
```


  2. ### Related Work 

  Our framework intersects three key areas of research: credit assignment in multi-agent systems, the application of Shapley values for attribution, and the automated optimization of large language models. We position our contributions in relation to the state of the art in each domain.

  2.1 Credit Assignment in Multi-Agent Systems

  The challenge of assigning credit or blame to individual agents within a cooperative team is a long-standing problem in multi-agent systems (MAS) (Wolpert and Tumer 2002). Classical approaches, particularly from the field of multi-agent reinforcement learning (MARL), have developed sophisticated methods to address this. Difference rewards and counterfactual analysis (Foerster et al. 2018) are prominent examples,  where an agent's contribution is estimated by comparing the global team reward to a baseline, such as the reward achievable without the agent's action. Further refinements in MARL, such as value decomposition networks (Sunehag et al. 2018) and actor-critic methods tailored for multi-agent settings (Lowe et al. 2017), have improved credit assignment but still fundamentally rely on well-defined state-action spaces. Methods like Shapley Q-learning (Wang et al. 2020) and other game-theoretic approaches (Han et al., 2022) have even attempted to integrate game-theoretic fairness directly into the learning process. However, these classical methods face significant limitations when applied to modern MAS composed of large language models (LLMs). First, the action space of an LLM agent is the vast, semantic space of natural language, making the definition of a "counterfactual action" ill-posed and computationally intractable. This contrasts sharply with the discrete or continuous action spaces in traditional MARL (Chen et al., 2022). Second, the black-box nature of proprietary LLMs prevents the use of gradient-based methods that are common in deep RL. Finally, rewards in complex, real-world tasks like financial trading are often sparse and delayed, making it difficult to trace a final portfolio outcome back to a specific textual analysis generated by an agent days or weeks prior, a challenge known as the temporal credit assignment problem (Ferret et al., 2019). Recent work has even begun to explore using LLMs themselves as critics for credit assignment (Nagpal et al., 2025), but this still often relies on heuristics. These challenges necessitate a shift from state-action-based credit assignment to a game-theoretic approach that evaluates the holistic contribution of agent coalitions, which our framework adopts.

  2.2 Shapley Values for Attribution and Explainability

Originating from cooperative game theory, the Shapley value (Shapley 1953) provides a uniquely fair and axiomatically sound method for distributing a collective gain among contributing players. Its desirable properties have led to its widespread adoption in machine learning for model explainability, most notably through SHAP (SHapley Additive exPlanations) (Lundberg and Lee 2017), which treats model features as players in a game to explain a prediction. The application of Shapley values has since expanded to diverse areas, including explaining cooperative strategies in MARL (Heuillet et al., 2021) and general reinforcement learning contexts (Beechey et al., 2023). Despite its theoretical elegance, the exact computation of Shapley values is notoriously intractable, requiring the evaluation of 2^N coalitions for N agents. This has spurred the development of numerous approximation methods. Stochastic approaches, such as Monte Carlo sampling of agent permutations (Castro, Gómez, and Tejada 2009) and its variants like Truncated Monte Carlo, offer a trade-off between accuracy and computation. More sophisticated techniques like KernelSHAP (Lundberg and Lee 2017) reframe the problem as a weighted linear regression, but still rely on sampling coalitions. Some work has explored exploiting structural information to make the computation more efficient. For instance, Myerson values, a generalization of Shapley values for games on graphs, have been used to explain multi-agent systems by considering a-priori knowledge of agent relationships (Angelotti and Díaz-Rodríguez, 2022). In stark contrast to these stochastic sampling-based methods, our proposed DAG-Shapley introduces a deterministic, structural pruning approach. While related to methods that use structural priors like Myerson values, DAG-Shapley is specifically tailored to the functional dependencies inherent in Directed Acyclic Graph (DAG) workflows. It does not approximate by sampling; instead, it leverages the known DAG structure to logically eliminate coalitions that are axiomatically guaranteed to yield zero value (e.g., those lacking a final decision-making agent). For this significant class of structured MAS, this approach is not only more computationally efficient but also avoids the variance and potential inaccuracies inherent in random sampling, providing a more robust and reliable attribution signal.

  2.3 Automated Prompt and Agent Optimization

The performance of LLMs is critically dependent on the quality of their prompts, making prompt engineering a key bottleneck. Research in automated prompt optimization aims to alleviate this. Early work like Automatic Prompt Engineer (APE) (Zhou et al. 2023) uses an LLM to generate and select candidate prompts for a given task in an offline setting. More recent frameworks like Promptomatix (Murthy et al., 2025) and Prochemy (Ye et al., 2025) have further automated this process, aiming to make prompt optimization more accessible and efficient. More recently, Optimization by PROmpting (OPRO) (Yang et al. 2023) demonstrated that an LLM can itself act as an optimizer, iteratively refining a prompt by observing its performance score on a task. This line of work is complemented by research into self-correcting and reflective agents, where an LLM critiques and refines its own outputs to improve performance. Frameworks like Reflexion (Shinn et al., 2023) and Recursive Introspection (Qu et al., 2024) show that agents can learn from feedback and iteratively improve. Other approaches, such as Devil's Advocate (Li et al., 2024), use anticipatory reflection to preemptively identify and remedy potential failures. Our Contribution-Guided Online Prompt Optimization (CG-OPO) builds upon these concepts but introduces three critical differentiators that are essential for self-adaptive MAS. First, our optimization is contribution-guided: it is not applied to a random or globally-scored agent but is precisely targeted at the agent identified as the weakest contributor by DAG-Shapley, ensuring that optimization effort is spent where it is most needed. Second, it is online: the optimization is not a one-shot, offline process but is integrated into a continuous operational loop, allowing the agent team to adapt and improve during its lifecycle. Finally, our process is reflective  and integrated: it forces the optimizer LLM to analyze concrete failure cases (e.g., trading days with financial losses) to generate "lessons learned," creating a more robust and context-aware prompt enhancement. This transforms prompt optimization from an isolated tool into a core component of a fully integrated, autonomous, and self-improving agent system.

---

  引用的文献列表 (List of Cited Papers)

   1. Angelotti, G., & Díaz-Rodríguez, N. (2022). Towards a more efficient computation of individual attribute and policy contribution for post-hoc explanation of cooperative multi-agent systems using Myerson values.

      

   2. ```
      @inproceedings{beechey2023explaining,
        title={Explaining reinforcement learning with shapley values},
        author={Beechey, Daniel and Smith, Thomas MS and {\c{S}}im{\c{s}}ek, {\"O}zg{\"u}r},
        booktitle={International Conference on Machine Learning},
        pages={2003--2014},
        year={2023},
        organization={PMLR}
      }
      ```

   3. 

   4. ```
      @article{castro2009polynomial,
        title={Polynomial calculation of the Shapley value based on sampling},
        author={Castro, Javier and G{\'o}mez, Daniel and Tejada, Juan},
        journal={Computers \& operations research},
        volume={36},
        number={5},
        pages={1726--1730},
        year={2009},
        publisher={Elsevier}
      }
      ```

   5. 

   6. ```
      @inproceedings{chen2023learning,
        title={Learning explicit credit assignment for cooperative multi-agent reinforcement learning via polarization policy gradient},
        author={Chen, Wubing and Li, Wenbin and Liu, Xiao and Yang, Shangdong and Gao, Yang},
        booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
        volume={37},
        number={10},
        pages={11542--11550},
        year={2023}
      }
      ```

   7. Collier, M., & Tumer, K. (2002). A method for evaluating the contributions of agents in a multiagent system.

   8. Ferret, J., Marinier, R., Geist, M., & Pietquin, O. (2019). Credit Assignment as a Proxy for Transfer in Reinforcement Learning.

   9. Foerster, J., Farquhar, G., Afouras, T., Nardelli, N., & Whiteson, S. (2018). Counterfactual multi-agent policy gradients.

   10. Han, D., Lu, C. X., Michalak, T., & Wooldridge, M. (2022). Multiagent Model-based Credit Assignment for Continuous Control.

   11. Heuillet, A., Couthouis, F., & Díaz-Rodríguez, N. (2021). Collective eXplainable AI: Explaining Cooperative Strategies and Agent Contribution in Multiagent Reinforcement Learning with Shapley Values.

   12. Li, T., et al. (2024). Devil's Advocate: Anticipatory Reflection for LLM Agents.

   13. Lowe, R., Wu, Y., Tamar, A., Harb, J., Abbeel, P., & Mordatch, I. (2017). Multi-agent actor-critic for mixed cooperative-competitive environments.

   14. Lundberg, S. M., & Lee, S. I. (2017). A unified approach to interpreting model predictions.

   15. Murthy, R., et al. (2025). Promptomatix: An Automatic Prompt Optimization Framework for Large Language Models.

   16. Nagpal, K., et al. (2025). Leveraging Large Language Models for Effective and Explainable Multi-Agent Credit Assignment.

   17. Qu, Y., et al. (2024). Recursive Introspection: Teaching Language Model Agents How to Self-Improve.

   18. Shapley, L. S. (1953). A value for n-person games.

   19. Shinn, N., et al. (2023). Reflexion: Language Agents with Verbal Reinforcement Learning.

   20. Sunehag, P., et al. (2018). Value-decomposition networks for cooperative multi-agent learning.

   21. Wang, J., et al. (2020). SHAQ: Incorporating Shapley Value Theory into Multi-Agent Q-Learning.

   22. Wolpert, D. H., & Tumer, K. (2002). Optimal payoff functions for members of collectives.

   23. Yang, C., et al. (2023). Large language models as optimizers.

   24. Ye, S., et al. (2025). Prompt Alchemy: Automatic Prompt Refinement for Enhancing Code Generation.

   25. Zhou, Y., et al. (2023). Large language models are human-level prompt engineers.

#### **3.1. System Architecture: MAS as a Directed Acyclic Graph**

To systematically manage agent interactions and enable efficient contribution analysis, we model our Multi-Agent System (MAS) as a Directed Acyclic Graph (DAG). This representation is not merely conceptual; it is a structural prior that reflects the inherent information flow in many complex, collaborative tasks where agents occupy specialized roles in a processing pipeline. This architectural choice is fundamental to the design and efficiency of our entire framework.

Formally, we define the system as a graph G = (V, E), where V is the set of N agents, $V = \{a_1, a_2, ..., a_N\}$, and E is the set of directed edges. An edge (a_i, a_j) ∈ E signifies that the output of agent a_i is a required input for agent a_j. Within this graph, we distinguish two critical subsets of nodes: the source nodes V_source ⊂ V, which have an in-degree of zero and process external information, and a single sink node a_T ∈ V, which has an out-degree of zero and produces the system's final, actionable output. This structure is explicitly defined in our implementation (see trading_simulator.py) through an adjacency list (agent_graph) and an execution-ordered list of layers (agent_layers), which represents a topological sort of the graph.

To ground this abstraction, we instantiate our framework within a multi-agent financial trading system designed for stock market analysis and decision-making \cite{xiao2024tradingagents,shavandi2022multi}. As illustrated in Figure 1, the system comprises seven agents organized into three distinct hierarchical layers:

1. **Analysis Layer (Source Nodes):** This layer consists of three independent specialist agents: a News Analyst Agent (NAA), a Technical Analyst Agent (TAA), and a Fundamental Analyst Agent (FAA). Each agent processes raw, heterogeneous market data (news feeds, price history, and financial statements, respectively) in parallel to produce a focused analytical summary. They form the set of source nodes, V_source.
2. **Outlook Layer (Intermediate Nodes):** This layer contains three agents that synthesize the outputs from the Analysis Layer: a Bullish Outlook Agent (BOA), a Bearish Outlook Agent (BeOA), and a Neutral Outlook Agent (NOA). These agents are explicitly prohibited from accessing the raw market data, forcing them to rely solely on the structured analyses provided by the layer below. This enforces a clear abstraction boundary and information hierarchy.
3. **Decision Layer (Sink Node):** A single Trader Agent (TRA) constitutes this final layer. It consumes the competing outlooks from the intermediate layer to produce a final, actionable trading decision (e.g., buy, sell, hold) and position size. This agent serves as the system's sink node, a_T.

This logical DAG is realized through a modular, service-oriented software architecture. A central PhaseCoordinator orchestrates the system's high-level operational phases (e.g., simulation, evaluation, optimization), while the TradingSimulator enforces the agent execution order according to the DAG's topological sort. A ServiceFactory manages the dependency injection of these components, ensuring a decoupled and maintainable system. Crucially, this architecture includes a data access controller that strictly enforces the information flow defined by the DAG, restricting agents to only the data they are permitted to see based on their layer \cite{han2015distributed,wang2016multi}.

This representation is not merely conceptual; it is a structural prior that reflects the inherent information flow in many complex, collaborative tasks where agents occupy specialized roles in a processing pipeline \cite{jang2025learning}. This architectural choice is fundamental to the design and efficiency of our entire framework.

Formally, we define the system as a graph G = (V, E), where V is the set of N agents, $V = \{a_1, a_2, ..., a_N\}$, and E is the set of directed edges. An edge (a_i, a_j) ∈ E signifies that the output of agent a_i is a required input for agent a_j. Within this graph, we distinguish two critical subsets of nodes: the source nodes V_source ⊂ V, which have an in-degree of zero and process external information, and a single sink node a_T ∈ V, which has an out-degree of zero and produces the system's final, actionable output. This structure is explicitly defined in our implementation (see trading_simulator.py) through an adjacency list (agent_graph) and an execution-ordered list of layers (agent_layers), which represents a topological sort of the graph \cite{zhadan2023multi}. The integration of Shapley value theory into this multi-agent framework provides a principled approach to credit assignment that complements our DAG-based architecture \cite{wang2024shapley,wang2022shaq}.

---

@article{jang2025learning,
  title={Learning multiple coordinated agents under directed acyclic graph constraints},
  author={Jang, Jaeyeon and Klabjan, Diego and Liu, Han and Patel, Nital S and Li, Xiuqi and Ananthanarayanan, Balakrishnan and Dauod, Husam and Juang, Tzung-Han},
  journal={Expert Systems with Applications},
  pages={127744},
  year={2025},
  publisher={Elsevier}
}

@article{zhadan2023multi,
  title={Multi-agent reinforcement learning-based adaptive heterogeneous DAG scheduling},
  author={Zhadan, Anastasia and Allahverdyan, Alexander and Kondratov, Ivan and Mikheev, Vikenty and Petrosian, Ovanes and Romanovskii, Aleksei and Kharin, Vitaliy},
  journal={ACM transactions on intelligent systems and technology},
  volume={14},
  number={5},
  pages={1--26},
  year={2023},
  publisher={ACM New York, NY}
}

@article{xiao2024tradingagents,
  title={TradingAgents: Multi-agents LLM financial trading framework},
  author={Xiao, Yijia and Sun, Edward and Luo, Di and Wang, Wei},
  journal={arXiv preprint arXiv:2412.20138},
  year={2024}
}

@article{shavandi2022multi,
  title={A multi-agent deep reinforcement learning framework for algorithmic trading in financial markets},
  author={Shavandi, Ali and Khedmati, Majid},
  journal={Expert Systems with Applications},
  volume={208},
  pages={118124},
  year={2022},
  publisher={Elsevier}
}

@article{wang2024shapley,
  title={Shapley Value Based Multi-Agent Reinforcement Learning: Theory, Method and Its Application to Energy Network},
  author={Wang, Jianhong},
  journal={arXiv preprint arXiv:2402.15324},
  year={2024}
}

@article{wang2022shaq,
  title={Shaq: Incorporating shapley value theory into multi-agent q-learning},
  author={Wang, Jianhong and Zhang, Yuan and Gu, Yunjie and Kim, Tae-Kyun},
  journal={Advances in Neural Information Processing Systems},
  volume={35},
  pages={5941--5954},
  year={2022}
}

@article{han2015distributed,
  title={Distributed coordination in multi-agent systems: a graph Laplacian perspective},
  author={Han, Zhi-min and Lin, Zhi-yun and Fu, Min-yue and Chen, Zhi-yong},
  journal={Frontiers of Information Technology \& Electronic Engineering},
  volume={16},
  number={6},
  pages={429--448},
  year={2015},
  publisher={Springer}
}

@article{wang2016multi,
  title={Multi-agent distributed coordination control: Developments and directions via graph viewpoint},
  author={Wang, Xiangke and Zeng, Zhiwen and Cong, Yirui},
  journal={Neurocomputing},
  volume={199},
  pages={204--218},
  year={2016},
  publisher={Elsevier}
}

---

### **3.2. DAG-Shapley: Efficient Contribution Attribution**

To formally attribute contributions within our Multi-Agent System (MAS), we model the collaborative workflow as a cooperative game. The set of agents `N = {a_1, ..., a_n}` constitutes the players. The performance of any given subset of these agents, known as a coalition `S ⊆ N`, is quantified by a characteristic function, `v(S)`. In our financial trading implementation, `v(S)` is defined as the Sharpe Ratio achieved by the coalition `S` over a specified trading period, a standard measure of risk-adjusted return.

The classical Shapley value (Shapley, 1953) provides a unique, fair distribution of the total surplus `v(N)` among the agents. The Shapley value, `φ_i(v)`, for an agent `a_i` is its weighted average marginal contribution to all possible coalitions, calculated as:

$$
\phi_i(v) = \sum_{S \subseteq N \setminus \{a_i\}} \frac{|S|!(n - |S| - 1)!}{n!} [v(S \cup \{a_i\}) - v(S)]
$$

While axiomatically fair, the classical Shapley value is computationally intractable for real-time systems, as it requires evaluating `v(S)` for all `2^n` possible coalitions. Our framework addresses this challenge through a two-stage optimization process that leverages the system's DAG structure.

#### **Stage 1: Pruning the Coalition Space with DAG-Shapley**

Instead of resorting to stochastic approximations, our DAG-Shapley method first leverages the inherent Directed Acyclic Graph (DAG) structure of the agent workflow to deterministically prune the coalition space. Our framework identifies a set of source nodes `V_source` (e.g., analysis agents `NAA, TAA, FAA`) which initiate the information flow, and a designated sink node `a_T` (e.g., the `TRA` agent) which executes the final action. This structure allows us to define the concept of a viable coalition.

**Definition 1 (Viable Coalition).** A coalition `S` is considered viable if and only if it is capable of executing a complete information-to-action workflow. Structurally, this requires `S` to contain the sink node `a_T` and at least one source node from `V_source`.

$$
\text{S is viable} \iff (a_T \in S) \land (S \cap V_{\text{source}} \neq \emptyset)
$$

Based on this definition, we introduce two deterministic pruning rules that drastically reduce the number of coalitions requiring simulation.

**Pruning Rule 1 (Endpoint Rule).** For any coalition `S` that does not include the sink node `a_T`, its characteristic value is axiomatically zero.
$$
\text{If } a_T \notin S, \text{ then } v(S) \equiv 0
$$
*Justification:* A coalition lacking the terminal agent `a_T` is incapable of executing the final action (e.g., placing a trade) required to interact with and affect the environment's outcome. Without this interaction, its measurable performance is zero.

**Pruning Rule 2 (Startpoint Rule).** For any coalition `S` that contains no agents from the source set `V_source`, its characteristic value is also axiomatically zero.
$$
\text{If } S \cap V_{\text{source}} = \emptyset, \text{ then } v(S) \equiv 0
$$
*Justification:* A coalition without at least one source agent lacks the initial information (e.g., market analysis) necessary to initiate a meaningful decision-making process. The information cascade within the DAG cannot begin, rendering the coalition functionally inert.

The `CoalitionManager` module implements these rules (Algorithm 1) to filter the `2^n` total coalitions into a much smaller set of viable coalitions, `C_sim`, that require simulation. For our 7-agent system, this reduces the number of evaluations from 128 to 56, a reduction of over 56%.

#### **Stage 2: Minimizing Evaluation Cost with Hierarchical Memoization (HM)**

While pruning reduces the *number* of simulations, Hierarchical Memoization minimizes the computational *cost* of the remaining evaluations. This technique extends the structural insight of the DAG to the execution level. We model the DAG as a set of `N_L` layers, `L_1, L_2, ..., L_{N_L}`. The key principle of HM is that any agent `a ∈ L_i` is a pure function whose output depends solely on the outputs of the agents present in the coalition from the preceding layer, `L_{i-1}`.

This allows us to cache and reuse agent computations. The total computational cost (`Cost_{HM}`) is no longer a function of the number of simulated coalitions `|C_sim|`, but is instead determined by the local topology of the DAG. The cost is the sum of a constant base cost for the first layer and a hierarchical propagation cost for all subsequent layers:

$$
\text{Cost}_{HM} = \underbrace{\sum_{a \in L_1} C(a)}_{\text{Base Cost}} + \underbrace{\sum_{i=2}^{N_L} \left[ (2^{|L_{i-1}|} - 1) \cdot \sum_{a \in L_i} C(a) \right]}_{\text{Hierarchical Propagation Cost}}
$$

where `C(a)` is the cost of a single execution of agent `a`, and `|L_{i-1}|` is the number of agents in the preceding layer.

*Justification:* The first layer `L_1` agents' inputs are from the external environment, which is constant for a given time step, so they each run only once. For any subsequent layer `L_i`, an agent's computation needs to be run only once for each unique combination of outputs from the non-empty subsets of `L_{i-1}`. The number of such unique input combinations is `2^{|L_{i-1}|} - 1`.

This HM strategy transforms the computational complexity. Instead of being driven by the global property `n` (total agents), the cost is now driven by the local property `max(|L_i|)` (the width of the widest layer). For our system, this reduces the total number of agent executions from hundreds in a naive simulation to merely 31, achieving near-optimal efficiency. Together, DAG-Shapley pruning and Hierarchical Memoization form a comprehensive, structure-aware framework for efficient and accurate contribution attribution.

The `CoalitionManager` module implements these rules to filter the set of all possible coalitions into a much smaller set of viable coalitions `C_sim` that require simulation, as detailed in Algorithm 1.

```latex
\begin{algorithm}[h]
\caption{GeneratePrunedCoalitions}
\label{alg:pruning}
\begin{algorithmic}[1]
\Require Set of all agents $N$, set of source agents $V_{\text{source}}$, sink agent $a_T$
\Ensure Set of viable coalitions $C_{\text{sim}}$, set of pruned coalitions $C_{\text{pruned}}$
\State $C_{\text{sim}} \leftarrow \emptyset$
\State $C_{\text{pruned}} \leftarrow \emptyset$
\State AllCoalitions $\leftarrow$ Generate all subsets of $N$
\For{each coalition $S$ in AllCoalitions}
    \If{$a_T \notin S$} \Comment{Endpoint Rule}
        \State Add $S$ to $C_{\text{pruned}}$
        \State \textbf{continue}
    \EndIf
    \If{$S \cap V_{\text{source}} = \emptyset$} \Comment{Startpoint Rule}
        \State Add $S$ to $C_{\text{pruned}}$
        \State \textbf{continue}
    \EndIf
    \State Add $S$ to $C_{\text{sim}}$
\EndFor
\State \Return $C_{\text{sim}}, C_{\text{pruned}}$
\end{algorithmic}
\end{algorithm}
```

```latex
\begin{algorithm}[h]
\caption{Hierarchical Memoization for Coalition Value Calculation (Stage 2 Optimization)}
\label{alg:memoization}
\begin{algorithmic}[1]
\Require Set of viable coalitions $C_{\text{sim}}$, Layered DAG $L_1, \ldots, L_{N_L}$
\Ensure Dictionary of all coalition values $V$

\State $V \leftarrow \{\emptyset: 0.0\}$ \Comment{Initialize with empty set value}
\State $\text{MemoCache} \leftarrow \{\}$ \Comment{Cache for agent outputs}

\For{$i \leftarrow 1$ to $N_L$} \Comment{Iterate through layers}
    \State \textbf{if} $i = 1$ \textbf{then} \Comment{Base Case: First Layer}
        \State $\text{InputSubsets} \leftarrow \{\text{ExternalState}\}$
    \State \textbf{else}
        \State $\text{InputSubsets} \leftarrow$ Generate all non-empty subsets of agents in $L_{i-1}$
    \EndIf
    
    \For{each $\text{agent}$ in $L_i$}
        \For{each $\text{input\_subset}$ in $\text{InputSubsets}$}
            \State $\text{input\_key} \leftarrow$ Generate key from $\text{agent}$ and $\text{input\_subset}$
            \State $\text{inputs} \leftarrow$ Retrieve outputs from $\text{MemoCache}$ for $\text{input\_subset}$
            \State $\text{output} \leftarrow$ Execute $\text{agent}$ with $\text{inputs}$
            \State $\text{MemoCache}[\text{input\_key}] \leftarrow \text{output}$
        \EndFor
    \EndFor
\EndFor

\For{each coalition $S$ in $C_{\text{sim}}$} \Comment{Calculate value for each viable coalition}
    \State $v(S) \leftarrow$ Run simulation for $S$ using results from $\text{MemoCache}$
    \State $V[S] \leftarrow v(S)$
\EndFor

\State \Return $V$
\end{algorithmic}
\end{algorithm}
```

---

### 3.3. Contribution-Guided Online Prompt Optimization (CG-OPO)

Once the DAG-Shapley scores have been computed, they provide a quantitative diagnosis of the multi-agent system's internal health, identifying which agents are contributing positively and which are detrimental to the collective goal. However, diagnosis without treatment is of limited value. The Contribution-Guided Online Prompt Optimization (CG-OPO) mechanism serves as the therapeutic component of our framework, creating a closed loop that translates attribution scores into targeted, autonomous improvements. It operationalizes the principle of self-adaptation by systematically refining the instructional prompts of underperforming agents.

#### **Triggering Optimization: Guided Target Selection**

The optimization process is not indiscriminate; it is precisely guided by the contribution scores. At the end of each operational cycle $t$ (a "week" in our implementation), the system identifies the target agent for optimization, denoted as $a^*$, by selecting the agent with the minimum DAG-Shapley score:
$$ a^* = \arg\min_{a_i \in V} \mu_i(t) $$
where $V$ is the set of all agents and $\mu_i(t)$ is the contribution score for agent $a_i$ in cycle $t$. This ensures that computational resources for optimization are focused where they are most needed, targeting the primary source of performance degradation within the team. If all agents exhibit a contribution score above a predefined threshold, the optimization step for that cycle is skipped, preventing unnecessary modifications to a well-functioning system.

#### **The Reflection Mechanism: Learning from Failure**

The cornerstone of CG-OPO is its novel **Loss-Oriented Reflection** mechanism. Instead of relying on generic prompt improvement strategies, our method forces the system to learn from its concrete, historical failures. This is achieved by invoking a meta-level LLM, tasked with acting as an "AI System Analyst."

The analyst LLM is provided with a curated history of the target agent $a^*$'s operational data from the preceding cycle, which we term `weekly_io_data`. This data is a structured log of state-action pairs, critically including:

1.  The complete input `state` the agent received, which contains market data, account status, and outputs from preceding agents.
2.  The `previous_day_return`, a direct financial feedback signal indicating the outcome of the prior day's collective decision.
3.  The agent's raw LLM response and the final, parsed action it took.

The analyst LLM is then prompted with a specialized reflection template, as exemplified in Figure [X]. This prompt explicitly directs the LLM to focus its analysis on instances of failure, identified as days where `previous_day_return < 0`.

---

**Figure [X]: The Loss-Oriented Reflection Prompt Template.** This template guides the analyst LLM to perform a root-cause analysis on the underperforming agent's failures.

```
You are an expert AI System Analyst. Your task is to analyze the performance of an underperforming agent based on its weekly input-output data and generate "Key Lessons Learned" to improve its future behavior.

**Agent to Analyze:** {agent_name}
**Recent Performance Score (Shapley Value):** {performance_score}

**Weekly I/O Data:**
{weekly_io_data_summary}
---
🔴 **Loss-Making Days Detailed Analysis (previous_day_return < 0):**
   - Record 1: Date: 2025-07-21, Prior Day Return: -0.0235
     - Input State: {{ 'cash': $950k, 'positions': {{'AAPL': 100}}, ... }}
     - Agent Decision: "Market sentiment is negative, recommend caution..."
   - Record 2: ...

🟢 **Profitable/Neutral Days Summary (previous_day_return >= 0):**
   - Record 1: Date: 2025-07-22, Prior Day Return: 0.0158
     - Decision Summary: "Positive market trend, consider increasing position..."
   - Record 2: ...
---

**Analysis Instructions:**
Based on the data provided, please perform the following analysis:
1.  **Focus on Loss-Making Days:** Critically examine the agent's decisions on days following a loss. Did its logic adapt, or did it repeat mistakes?
2.  **Compare Winning vs. Losing Patterns:** What were the key differences in the agent's reasoning and the input state on profitable days versus losing days?
3.  **Identify Flawed Heuristics:** Pinpoint any recurring flawed logic, risk misjudgments, or missed opportunities in the agent's decision-making process.

**Your Output:**
Generate a concise, actionable list of "Key Lessons Learned" in bullet points. These lessons will be directly appended to the agent's prompt to guide its future actions.
```

By contrasting specific instances of failure with successful decisions, the analyst LLM performs a targeted, evidence-based review. It moves beyond syntax to critique the agent's underlying decision-making heuristics, generating a set of actionable "Key Lessons Learned" that address the root causes of underperformance.

#### **Prompt Metamorphosis**

The final step is to integrate the generated wisdom back into the target agent. The CG-OPO process employs a simple yet effective technique of **prompt augmentation**, which we term Prompt Metamorphosis. The original prompt of the agent, $P_t(a^*)$, which defines its core function, is preserved. The generated lessons, $L_t$, are appended to it to form the new, enhanced prompt for the next cycle, $P_{t+1}(a^*)$:

$$ P_{t+1}(a^*) \leftarrow P_t(a^*) \oplus \text{"Based on your recent performance, consider these key lessons: "} \oplus L_t $$

where $\oplus$ denotes the string concatenation operator. This approach has the dual benefit of maintaining the agent's foundational identity while injecting targeted, experience-based corrective guidance. This iterative refinement allows agents to evolve and adapt their strategies over time, driven directly by their measured contribution to the team's success. The entire CG-OPO workflow is summarized in Algorithm 1.

---

**Algorithm 3:** Contribution-Guided Online Prompt Optimization (CG-OPO)

```latex
\begin{algorithmic}[1]
\State \textbf{Input:} Agent set $V$, agent prompts $\{P_t(a_i)\}_{i=1}^n$, contribution scores $\{\mu_t(a_i)\}_{i=1}^n$.
\State \textbf{Output:} An updated prompt $P_{t+1}(a^*)$ for the target agent.
\State
\Function{CG-OPO}{$V, \{P_t\}, \{\mu_t\}$}
    \State $a^* \leftarrow \arg\min_{a_i \in V} \mu_t(a_i)$ \Comment{Identify worst-performing agent}
    \If{$\mu_t(a^*) < \text{threshold}$}
        \State $H^* \leftarrow \text{GetWeeklyIOData}(a^*)$ \Comment{Retrieve historical I/O data}
        \State $C^* \leftarrow \text{FormatFailureCases}(H^*)$ \Comment{Format loss-oriented context}
        \State $\Pi_{\text{reflect}} \leftarrow \text{ConstructReflectionPrompt}(a^*, \mu_t(a^*), C^*)$
        \State $L_t \leftarrow \text{LLM}(\Pi_{\text{reflect}})$ \Comment{Generate Key Lessons Learned}
        \State $P_{t+1}(a^*) \leftarrow P_t(a^*) \oplus L_t$ \Comment{Perform Prompt Metamorphosis}
        \State \textbf{return} $P_{t+1}(a^*)$
    \Else
        \State \textbf{return} $P_t(a^*)$ \Comment{No optimization needed}
    \EndIf
\EndFunction
\end{icod}
\end{algorithmic}
```

